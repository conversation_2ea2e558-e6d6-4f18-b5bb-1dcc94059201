import { auth, currentUser, clerkClient } from '@clerk/nextjs/server'
import { headers } from 'next/headers'
import { redirect } from 'next/navigation'
import type { UserRole } from './schemas'
import { supabaseAdmin } from './supabase'

// Get current user with role information
export async function getCurrentUser() {
  const { userId } = await auth()

  if (!userId) {
    console.log('🚫 [AUTH DEBUG] No userId found in auth()')
    return null
  }

  const user = await currentUser()

  if (!user) {
    console.log('🚫 [AUTH DEBUG] No user found from currentUser()')
    return null
  }

  const userEmail = user.emailAddresses[0]?.emailAddress
  const fullName = `${user.firstName} ${user.lastName}`.trim()

  console.log('👤 [AUTH DEBUG] Current user from Clerk:', {
    id: userId,
    email: userEmail,
    fullName,
    firstName: user.firstName,
    lastName: user.lastName
  })

  // Special logging for CJN Automation debugging
  if (userEmail === '<EMAIL>') {
    console.log('🔍 [CJN DEBUG] CJN Automation login detected:', {
      clerkId: userId,
      email: userEmail,
      fullName: fullName
    })
  }

  // First, try to sync/update user in database based on email
  let databaseUserId = userId
  let role: UserRole = 'manager'

  if (userEmail) {
    console.log('🔄 [AUTH DEBUG] Syncing user with database based on email:', userEmail)

    // Check if user exists in database by email
    const { data: existingManager, error: lookupError } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id, full_name, role, active')
      .eq('email', userEmail)
      .single()

    if (lookupError && lookupError.code !== 'PGRST116') {
      console.error('🚨 [AUTH ERROR] Database lookup error:', lookupError)
    }

    if (existingManager) {
      console.log('📧 [AUTH DEBUG] Found existing manager by email:', existingManager)

      // Special logging for CJN Automation
      if (userEmail === '<EMAIL>') {
        console.log('🔍 [CJN DEBUG] Found CJN in database:', {
          existingUserId: existingManager.user_id,
          clerkUserId: userId,
          needsUpdate: existingManager.user_id !== userId
        })
      }

      // If the user_id doesn't match the Clerk ID, update it
      if (existingManager.user_id !== userId) {
        console.log('🔄 [AUTH DEBUG] Updating user_id from', existingManager.user_id, 'to', userId)

        const { error: updateError } = await supabaseAdmin
          .from('appy_managers')
          .update({
            user_id: userId,
            full_name: fullName // Also update name in case it changed
          })
          .eq('email', userEmail)

        if (updateError) {
          console.error('🚨 [AUTH ERROR] Failed to update user_id:', updateError)
        } else {
          console.log('✅ [AUTH DEBUG] Successfully updated user_id for', userEmail)
        }
      }

      databaseUserId = userId // Use the Clerk ID as the canonical ID
    } else {
      console.log('➕ [AUTH DEBUG] No existing manager found, creating new one')

      // Special logging for CJN Automation
      if (userEmail === '<EMAIL>') {
        console.log('🚨 [CJN ERROR] CJN Automation not found in database! This should not happen.')
      }

      // Create new manager entry
      const { error: insertError } = await supabaseAdmin
        .from('appy_managers')
        .insert({
          user_id: userId,
          full_name: fullName,
          email: userEmail,
          active: true,
          role: 'manager' // Default role
        })

      if (insertError) {
        console.error('🚨 [AUTH ERROR] Failed to create new manager:', insertError)
      }
    }
  }

  // Get user role from unified appy_managers table
  const { data: managerData, error: managerError } = await supabaseAdmin
    .from('appy_managers')
    .select('role, full_name, email')
    .eq('user_id', databaseUserId)
    .single()

  console.log('🔍 [AUTH DEBUG] Manager role query:', {
    userId: databaseUserId,
    result: managerData,
    error: managerError
  })

  // Special logging for CJN Automation
  if (userEmail === '<EMAIL>') {
    console.log('🔍 [CJN DEBUG] Database lookup result:', {
      databaseUserId,
      managerData,
      managerError: managerError?.message
    })
  }

  if (managerData?.role) {
    role = managerData.role
    console.log('🔍 [AUTH DEBUG] User role from appy_managers table:', role)
  } else {
    role = 'manager' // Default role for all managers
    console.log('🔍 [AUTH DEBUG] Using default manager role')
  }

  const finalUserData = {
    id: databaseUserId,
    email: userEmail,
    fullName,
    role
  }

  console.log('✅ [AUTH DEBUG] Final user data:', finalUserData)

  // Special logging for CJN Automation
  if (userEmail === '<EMAIL>') {
    console.log('🔍 [CJN DEBUG] Final CJN user data:', finalUserData)
  }

  return {
    id: databaseUserId,
    email: userEmail || '',
    fullName,
    role,
    imageUrl: user.imageUrl,
  }
}

// Get user role from headers (set by middleware)
export async function getUserRoleFromHeaders(): Promise<UserRole> {
  const headersList = await headers()
  const role = headersList.get('x-user-role') as UserRole
  return role || 'manager'
}

// Get user ID from headers (set by middleware)
export async function getUserIdFromHeaders(): Promise<string | null> {
  const headersList = await headers()
  return headersList.get('x-user-id')
}

// Check if user is super-admin
export function isSuperAdmin(userRole: UserRole): boolean {
  return userRole === 'super-admin'
}

// Check if user has super-admin access (should bypass all restrictions)
export function hasSuperAdminAccess(user: { role: UserRole } | null): boolean {
  return user?.role === 'super-admin'
}

// Check if user has specific permission
export function hasPermission(userRole: UserRole, permission: string): boolean {
  const rolePermissions = {
    'super-admin': ['*'],
    'hr-admin': [
      'employee:read', 'employee:write', 'employee:delete',
      'department:read', 'department:write', 'department:delete',
      'period:read', 'period:write', 'period:delete',
      'appraisal:read', 'appraisal:write', 'appraisal:approve',
      'approval:read',
      'pto:read', 'pto:write', 'pto:approve'
    ],
    'senior-manager': [
      'employee:read', 'employee:write',
      'department:read',
      'period:read',
      'appraisal:read', 'appraisal:write', 'appraisal:approve',
      'approval:read',
      'pto:read', 'pto:write', 'pto:approve'
    ],
    'admin': [
      'employee:read',
      'department:read',
      'period:read',
      'appraisal:read',
      'approval:read',
      'pto:read'
    ],
    'manager': [
      'employee:read',
      'appraisal:read', 'appraisal:write',
      'approval:read',
      'pto:read', 'pto:write', 'pto:approve'
    ],
    'accountant': [
      'employee:read',
      'appraisal:read',
      'approval:read', 'approval:export',
      'pto:read'
    ]
  }

  const permissions = rolePermissions[userRole] || []
  return permissions.includes('*') || permissions.includes(permission)
}

// Require authentication (redirect if not authenticated)
export async function requireAuth() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/sign-in')
  }
  
  return user
}

// Require specific role (redirect if insufficient permissions)
export async function requireRole(requiredRole: UserRole | UserRole[]) {
  const user = await requireAuth()
  
  const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
  
  if (!allowedRoles.includes(user.role)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return user
}

// Require specific permission (redirect if insufficient permissions)
export async function requirePermission(permission: string) {
  const user = await requireAuth()
  
  if (!hasPermission(user.role, permission)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
  
  return user
}

// Check if current user has super-admin access
export async function isCurrentUserSuperAdmin(): Promise<boolean> {
  const user = await getCurrentUser()
  return hasSuperAdminAccess(user)
}

// Check if current user can access employee data
export async function canAccessEmployee(employeeId: string): Promise<boolean> {
  const user = await getCurrentUser()
  
  if (!user) return false
  
  // Super admin and HR admin can access all employees
  if (user.role === 'super-admin' || user.role === 'hr-admin') {
    return true
  }
  
  // Managers can only access their assigned employees
  if (user.role === 'manager') {
    // TODO: Check if this employee is assigned to this manager
    // This would require a database query to check employee_assignments table
    return true // For now, allow all managers to access all employees
  }

  // Accountants can read employee data for approvals
  if (user.role === 'accountant') {
    return true
  }
  
  return false
}

// Validate user session and return user info
export async function validateSession() {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      throw new Error('No user session found')
    }
    
    const user = await currentUser()
    
    if (!user) {
      throw new Error('User not found')
    }
    
    return {
      userId,
      email: user.emailAddresses[0]?.emailAddress,
      fullName: `${user.firstName} ${user.lastName}`.trim(),
      role: (user.publicMetadata?.role as UserRole) || 'manager',
      isActive: true,
    }
  } catch (error) {
    console.error('Session validation failed:', error)
    throw new Error('Invalid session')
  }
}

// Rate limiting helper
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  userId: string, 
  action: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): boolean {
  const key = `${userId}:${action}`
  const now = Date.now()
  const userLimit = rateLimitMap.get(key)
  
  if (!userLimit || now > userLimit.resetTime) {
    // Reset or initialize rate limit
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (userLimit.count >= maxRequests) {
    return false // Rate limit exceeded
  }
  
  userLimit.count++
  return true
}

// Audit logging helper
export async function logUserAction(
  action: string, 
  details?: Record<string, any>
) {
  const user = await getCurrentUser()
  
  if (!user) return
  
  const logEntry = {
    userId: user.id,
    userRole: user.role,
    action,
    details,
    timestamp: new Date().toISOString(),
    ip: (await headers()).get('x-forwarded-for') || 'unknown',
    userAgent: (await headers()).get('user-agent') || 'unknown',
  }
  
  // In production, send this to your logging service
  console.log('User Action:', logEntry)
  
  // TODO: Store in database or send to external logging service
}

// Check if user can approve appraisals
export function canApproveAppraisals(userRole: UserRole): boolean {
  return userRole === 'super-admin' || userRole === 'hr-admin' || userRole === 'senior-manager'
}

// Check if user can approve specific appraisal based on role hierarchy
export async function canApproveAppraisal(appraisalSubmittedBy: UserRole): Promise<boolean> {
  const currentUser = await getCurrentUser()
  if (!currentUser) return false

  // Super admin can approve everyone's appraisals
  if (currentUser.role === 'super-admin') {
    return true
  }

  // HR admin can approve appraisals from managers and senior-managers
  if (currentUser.role === 'hr-admin') {
    return appraisalSubmittedBy === 'manager' || appraisalSubmittedBy === 'senior-manager'
  }

  // Senior-managers can approve appraisals from their direct reports
  if (currentUser.role === 'senior-manager') {
    return appraisalSubmittedBy === 'manager'
  }

  return false
}

// Check if user can approve specific appraisal for a specific employee
export async function canApproveEmployeeAppraisal(employeeId: string): Promise<boolean> {
  const currentUser = await getCurrentUser()
  if (!currentUser) return false

  // Super admin and HR admin can approve all appraisals
  if (currentUser.role === 'super-admin' || currentUser.role === 'hr-admin') {
    return true
  }

  // Senior-managers can approve appraisals for employees in their department hierarchy
  if (currentUser.role === 'senior-manager') {
    try {
      const { supabaseAdmin } = await import('./supabase')
      
      // Check if employee is in the same department as the senior-manager
      const { data: employeeData } = await supabaseAdmin
        .from('appy_employees')
        .select('department_id, manager_id')
        .eq('id', employeeId)
        .single()

      if (!employeeData) return false

      // Get current user's department
      const { data: managerData } = await supabaseAdmin
        .from('appy_managers')
        .select('department_id, user_id')
        .eq('user_id', currentUser.id)
        .single()

      if (!managerData) return false

      // Check if employee is in same department
      if (employeeData.department_id === managerData.department_id) {
        return true
      }

      // Check if employee's manager reports to this senior-manager
      const { data: employeeManagerData } = await supabaseAdmin
        .from('appy_managers')
        .select('manager_id, department_id')
        .eq('user_id', employeeData.manager_id)
        .single()

      if (employeeManagerData?.manager_id === currentUser.id) {
        return true
      }

    } catch (error) {
      console.error('Error checking approval permissions:', error)
      return false
    }
  }

  return false
}

// Get approval permission level for current user
export async function getApprovalLevel(): Promise<'super-admin' | 'hr-admin' | 'senior-manager' | 'none'> {
  const user = await getCurrentUser()
  if (!user) return 'none'

  if (user.role === 'super-admin') return 'super-admin'
  if (user.role === 'hr-admin') return 'hr-admin'
  if (user.role === 'senior-manager') return 'senior-manager'
  
  return 'none'
}

// Check if user requires approval for their appraisals
export function requiresApproval(userRole: UserRole): boolean {
  // Everyone except super-admin requires approval
  return userRole !== 'super-admin'
}

// Manual sync function for troubleshooting
export async function forceSyncUser(email: string) {
  console.log('🔄 [FORCE SYNC] Starting manual sync for:', email)

  try {
    // Get user from Clerk by email
    const clerk = await clerkClient()
    const users = await clerk.users.getUserList({
      emailAddress: [email],
      limit: 1
    })

    if (users.data.length === 0) {
      console.error('🚨 [FORCE SYNC] User not found in Clerk:', email)
      return { success: false, error: 'User not found in Clerk' }
    }

    const clerkUser = users.data[0]
    const clerkId = clerkUser.id
    const fullName = `${clerkUser.firstName} ${clerkUser.lastName}`.trim()

    console.log('👤 [FORCE SYNC] Found Clerk user:', { clerkId, email, fullName })

    // Check database
    const { data: existingManager, error: lookupError } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', email)
      .single()

    if (lookupError && lookupError.code !== 'PGRST116') {
      console.error('🚨 [FORCE SYNC] Database lookup error:', lookupError)
      return { success: false, error: lookupError.message }
    }

    if (existingManager) {
      console.log('📧 [FORCE SYNC] Found existing manager:', existingManager)

      // Update user_id if different
      if (existingManager.user_id !== clerkId) {
        console.log('🔄 [FORCE SYNC] Updating user_id from', existingManager.user_id, 'to', clerkId)

        const { error: updateError } = await supabaseAdmin
          .from('appy_managers')
          .update({
            user_id: clerkId,
            full_name: fullName
          })
          .eq('email', email)

        if (updateError) {
          console.error('🚨 [FORCE SYNC] Update failed:', updateError)
          return { success: false, error: updateError.message }
        }

        console.log('✅ [FORCE SYNC] Successfully updated user_id')
      } else {
        console.log('✅ [FORCE SYNC] User_id already matches, no update needed')
      }

      return {
        success: true,
        data: {
          clerkId,
          email,
          fullName,
          role: existingManager.role,
          wasUpdated: existingManager.user_id !== clerkId
        }
      }
    } else {
      console.log('🚨 [FORCE SYNC] Manager not found in database:', email)
      return { success: false, error: 'Manager not found in database' }
    }

  } catch (error) {
    console.error('🚨 [FORCE SYNC] Sync failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export type AuthUser = Awaited<ReturnType<typeof getCurrentUser>>
export type SessionUser = Awaited<ReturnType<typeof validateSession>>
