import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Get display name for employee department, handling executives gracefully
 */
export function getEmployeeDepartmentDisplay(employee: { fullName?: string; departmentName?: string | null; role?: string }): string {
  if (employee.departmentName) {
    return employee.departmentName
  }

  // Handle executives and senior roles without departments more gracefully
  const name = employee.fullName?.toLowerCase() || ''
  const role = employee.role?.toLowerCase() || ''

  if (name.includes('coo') || name.includes('ceo') || name.includes('cto') ||
      role.includes('executive') || role.includes('chief') ||
      employee.fullName === '<PERSON>') {
    return 'Executive Team'
  }

  return 'Unassigned'
}

export function generateUserId(): string {
  // Generate a user ID similar to Clerk's format for compatibility
  return `user_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`
}
