import type { Employee, EmployeeDetails, EmployeeKPI } from '../types'
import { db } from '../db'
import { supabase, supabaseAdminQuery } from '../supabase'

export async function getEmployees(): Promise<Employee[]> {
  try {
    console.log('📋 [DEBUG] getEmployees - Starting to fetch all employees')
    const employees = await db.getEmployees()
    console.log('📋 [DEBUG] getEmployees - Fetched', employees.length, 'employees from database')
    
    const mappedEmployees = employees.map(emp => ({
      id: emp.id,
      fullName: emp.full_name,
      compensation: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      departmentId: emp.department_id || '',
      departmentName: emp.department_name,
      managerId: emp.manager_id, // Legacy field - will be primary manager ID
      managerName: emp.manager_name, // Legacy field - will be primary manager name
      managerIds: emp.manager_ids || [], // New field - all manager IDs
      managers: (emp.managers || []).map(m => ({
        id: `${emp.id}-${m.managerId}`, // Generate stable ID for EmployeeManager type compatibility
        employeeId: emp.id,
        managerId: m.managerId,
        managerName: m.managerName,
        isPrimary: m.isPrimary,
        assignedAt: m.assignedAt
      })), // New field - full manager details
      active: emp.active
    }))
    
    console.log('📋 [DEBUG] getEmployees - Returning', mappedEmployees.length, 'mapped employees')
    return mappedEmployees
  } catch (error) {
    console.error('❌ [ERROR] getEmployees - Failed to fetch employees from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveEmployee(emp: Partial<Employee>): Promise<void> {
  try {
    if (process.env.ENABLE_DEBUG_LOGS === 'true') {
      console.log('🔧 [DEBUG] saveEmployee called with:', {
        id: emp.id,
        fullName: emp.fullName,
        email: emp.email,
        isUpdate: !!emp.id
      })
    }

    if (emp.id) {
      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('🔄 [DEBUG] Updating existing employee with ID:', emp.id)
      }

      // Verify employee exists before updating
      const existingEmployee = await db.getEmployeeById(emp.id)
      if (!existingEmployee) {
        console.error('❌ [ERROR] Employee not found for update:', emp.id)
        throw new Error(`Employee with ID ${emp.id} not found`)
      }

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [DEBUG] Employee found, proceeding with update')
      }

      // Split fullName into first and last name for consistency
      const nameParts = emp.fullName?.split(' ') || []
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      await db.updateEmployee(emp.id, {
        fullName: emp.fullName,
        firstName: firstName,
        lastName: lastName,
        email: emp.email,
        role: emp.role,
        bio: emp.bio,
        linkedinUrl: emp.linkedinUrl,
        twitterUrl: emp.twitterUrl,
        telegramUrl: emp.telegramUrl,
        compensation: 1000, // Default placeholder value for DB compatibility
        rate: emp.compensation, // DB expects compensation type in rate field
        departmentId: emp.departmentId,
        managerId: emp.managerId || undefined,
        active: emp.active
      })

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [DEBUG] Employee updated successfully')
      }
    } else {
      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('➕ [DEBUG] Creating new employee')
      }

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('🔧 [DEBUG] Creating employee with mapped fields:', {
          fullName: emp.fullName,
          email: emp.email,
          compensation: 1000, // Default placeholder value for DB compatibility
          rate: emp.compensation, // DB expects compensation type in rate field
          departmentId: emp.departmentId,
          managerId: emp.managerId || undefined
        })
      }

      // Split fullName into first and last name for consistency
      const nameParts = emp.fullName?.split(' ') || []
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      await db.createEmployee({
        fullName: emp.fullName!,
        firstName: firstName,
        lastName: lastName,
        email: emp.email!,
        role: emp.role,
        bio: emp.bio,
        linkedinUrl: emp.linkedinUrl,
        twitterUrl: emp.twitterUrl,
        telegramUrl: emp.telegramUrl,
        compensation: 1000, // Default placeholder value for DB compatibility
        rate: emp.compensation!, // DB expects compensation type in rate field
        departmentId: emp.departmentId!,
        managerId: emp.managerId || undefined
      })

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [DEBUG] Employee created successfully')
      }
    }
  } catch (error) {
    console.error('❌ [ERROR] Failed to save employee to database:', error)

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('unique constraint') && error.message.includes('email')) {
        throw new Error('An employee with this email address already exists')
      } else if (error.message.includes('foreign key')) {
        throw new Error('Invalid department or manager selected')
      } else {
        throw new Error(`Failed to save employee: ${error.message}`)
      }
    }

    throw new Error('Failed to save employee')
  }
}

export async function getEmployeeDetails(employeeId: string): Promise<EmployeeDetails | null> {
  try {
    const employee = await db.getEmployeeById(employeeId)
    if (!employee) return null

    return {
      id: employee.id,
      fullName: employee.full_name,
      departmentName: employee.department_name || 'N/A',
      compensation: employee.rate as 'hourly' | 'monthly', // DB rate field contains compensation type
    }
  } catch (error) {
    console.error('Failed to fetch employee details:', error)
    return null
  }
}

/**
 * Get all employees under a manager's hierarchy (including sub-managers and their employees)
 * @param managerId The manager's user ID
 * @returns Array of employees in the hierarchy with their hierarchy level
 */
export async function getEmployeesForManager(managerId: string): Promise<Employee[]> {
  try {
    console.log('🔍 [EMPLOYEES DEBUG] getEmployeesForManager - Fetching hierarchical employees for manager:', managerId)

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.log('🔍 [CJN EMPLOYEES DEBUG] Fetching employees for CJN Automation:', managerId)
    }

    const { data, error } = await supabase.rpc('get_hierarchical_employees', {
      manager_user_id: managerId
    })

    if (error) {
      console.error('🚨 [ERROR] getEmployeesForManager - Supabase RPC error:', error)
      if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
        console.error('🚨 [CJN ERROR] CJN Automation RPC error:', error)
      }
      throw error
    }

    if (!data) {
      console.log('📝 [EMPLOYEES DEBUG] getEmployeesForManager - No data returned for manager:', managerId)
      if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
        console.log('📝 [CJN DEBUG] No data returned for CJN Automation')
      }
      return []
    }

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.log('🔍 [CJN EMPLOYEES DEBUG] Raw data from RPC:', data)
    }

    console.log('✅ [DEBUG] getEmployeesForManager - Found', data.length, 'employees in hierarchy')
    console.log('📊 [DEBUG] getEmployeesForManager - Raw data sample:', data[0])
    console.log('📊 [DEBUG] getEmployeesForManager - Hierarchy breakdown:',
      data.reduce((acc: any, emp: any) => {
        acc[`Level ${emp.hierarchy_level}`] = (acc[`Level ${emp.hierarchy_level}`] || 0) + 1
        return acc
      }, {})
    )

    // Transform the data to match our Employee type
    const employees: Employee[] = data.map((emp: any) => ({
      id: emp.id,
      fullName: emp.full_name,
      compensation: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      departmentId: emp.department_id || '',
      departmentName: emp.department_name || '',
      managerId: emp.manager_id,
      managerName: emp.manager_name || '',
      active: emp.active
    }))

    console.log('🎯 [EMPLOYEES DEBUG] getEmployeesForManager - Returning', employees.length, 'formatted employees')
    console.log('🎯 [EMPLOYEES DEBUG] getEmployeesForManager - Sample employee:', employees[0])

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.log('🎯 [CJN EMPLOYEES DEBUG] Final result for CJN Automation:', {
        count: employees.length,
        employees: employees.map(e => ({ id: e.id, name: e.fullName, managerId: e.managerId }))
      })
    }

    return employees

  } catch (error) {
    console.error('🚨 [ERROR] getEmployeesForManager - Failed to fetch hierarchical employees:', error)

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.error('🚨 [CJN ERROR] Failed to fetch employees for CJN Automation:', error)
    }

    // Fallback to empty array in case of error
    return []
  }
}

/**
 * Get employee profile with all details
 * @param employeeId The employee ID
 * @returns Employee with profile fields or null
 */
export async function getEmployeeProfile(employeeId: string): Promise<Employee | null> {
  try {
    // Use the database function that now includes multi-manager support
    const employeeWithDetails = await db.getEmployeeById(employeeId)
    
    if (!employeeWithDetails) return null

    return {
      id: employeeWithDetails.id,
      fullName: employeeWithDetails.full_name,
      firstName: employeeWithDetails.first_name || undefined,
      lastName: employeeWithDetails.last_name || undefined,
      email: employeeWithDetails.email || undefined,
      role: employeeWithDetails.role || undefined,
      bio: employeeWithDetails.bio || undefined,
      linkedinUrl: employeeWithDetails.linkedin_url || undefined,
      twitterUrl: employeeWithDetails.twitter_url || undefined,
      telegramUrl: employeeWithDetails.telegram_url || undefined,
      compensation: employeeWithDetails.rate,
      departmentId: employeeWithDetails.department_id || '',
      departmentName: employeeWithDetails.department_name,
      managerId: employeeWithDetails.manager_id, // Legacy field - primary manager
      managerName: employeeWithDetails.manager_name, // Legacy field - primary manager name
      managerIds: employeeWithDetails.manager_ids || [], // New field - all manager IDs
      managers: (employeeWithDetails.managers || []).map(m => ({
        id: `${employeeWithDetails.id}-${m.managerId}`,
        employeeId: employeeWithDetails.id,
        managerId: m.managerId,
        managerName: m.managerName,
        isPrimary: m.isPrimary,
        assignedAt: m.assignedAt
      })), // New field - full manager details
      active: employeeWithDetails.active,
      updatedAt: employeeWithDetails.updated_at || undefined
    }
  } catch (error) {
    console.error('Failed to fetch employee profile:', error)
    return null
  }
}

/**
 * Get employee KPIs
 * @param employeeId The employee ID
 * @returns Array of KPIs
 */
export async function getEmployeeKPIs(employeeId: string): Promise<EmployeeKPI[]> {
  try {
    const { data, error } = await supabaseAdminQuery
      .employeeKpis()
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching employee KPIs:', error)
      return []
    }

    return data.map(kpi => ({
      id: kpi.id,
      employeeId: kpi.employee_id,
      kpiName: kpi.kpi_name,
      kpiValue: kpi.kpi_value || undefined,
      kpiTarget: kpi.kpi_target || undefined,
      kpiUnit: kpi.kpi_unit || undefined,
      period: kpi.period || undefined,
      description: kpi.description || undefined,
      createdAt: kpi.created_at,
      updatedAt: kpi.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch employee KPIs:', error)
    return []
  }
}