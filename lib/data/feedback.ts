import { supabaseAdmin, supabaseAdminQuery } from '../supabase'
import type { 
  EmployeeFeedback, 
  FeedbackFormData, 
  FeedbackComment, 
  FeedbackStatusHistory, 
  FeedbackStatistics,
  FeedbackStatus,
  FeedbackType,
  FeedbackCategory,
  FeedbackPriority
} from '../types'
import { debug } from '../debug'

/**
 * Submit new employee feedback
 */
export async function submitFeedback(
  submitterId: string, 
  feedbackData: FeedbackFormData
): Promise<{ success: boolean; feedbackId?: string; error?: string }> {
  try {
    debug.log('📝 [FEEDBACK] Submitting new feedback:', {
      submitterId,
      type: feedbackData.feedbackType,
      isAnonymous: feedbackData.isAnonymous
    })

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .insert({
        submitter_id: submitterId,
        target_employee_id: feedbackData.targetEmployeeId || null,
        feedback_type: feedbackData.feedbackType,
        category: feedbackData.category || null,
        subject: feedbackData.subject,
        message: feedbackData.message,
        is_anonymous: feedbackData.isAnonymous,
        priority: feedbackData.priority,
        status: 'pending'
      })
      .select('id')
      .single()

    if (error) {
      console.error('❌ [FEEDBACK] Failed to submit feedback:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [FEEDBACK] Feedback submitted successfully:', data.id)
    return { success: true, feedbackId: data.id }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error submitting feedback:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Get feedback submitted by a specific employee
 */
export async function getEmployeeFeedback(employeeId: string): Promise<EmployeeFeedback[]> {
  try {
    debug.log('📋 [FEEDBACK] Fetching feedback for employee:', employeeId)

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .select(`
        *,
        target_employee:target_employee_id(full_name),
        reviewed_by_manager:reviewed_by(full_name),
        resolved_by_manager:resolved_by(full_name)
      `)
      .eq('submitter_id', employeeId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch employee feedback:', error)
      return []
    }

    return (data || []).map(mapFeedbackFromDB)

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching employee feedback:', error)
    return []
  }
}

/**
 * Get all pending feedback for HR review
 */
export async function getPendingFeedbackForHR(): Promise<EmployeeFeedback[]> {
  try {
    debug.log('🔍 [FEEDBACK] Fetching pending feedback for HR')

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .select(`
        *,
        submitter:submitter_id(full_name, email),
        target_employee:target_employee_id(full_name, email),
        reviewed_by_manager:reviewed_by(full_name),
        resolved_by_manager:resolved_by(full_name)
      `)
      .in('status', ['pending', 'under_review'])
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch pending feedback:', error)
      return []
    }

    return (data || []).map(mapFeedbackFromDB)

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching pending feedback:', error)
    return []
  }
}

/**
 * Get detailed feedback by ID (for HR review)
 */
export async function getFeedbackById(feedbackId: string): Promise<EmployeeFeedback | null> {
  try {
    debug.log('🔍 [FEEDBACK] Fetching feedback details:', feedbackId)

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .select(`
        *,
        submitter:submitter_id(full_name, email),
        target_employee:target_employee_id(full_name, email),
        reviewed_by_manager:reviewed_by(full_name),
        resolved_by_manager:resolved_by(full_name)
      `)
      .eq('id', feedbackId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('❌ [FEEDBACK] Failed to fetch feedback details:', error)
      return null
    }

    return mapFeedbackFromDB(data)

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching feedback details:', error)
    return null
  }
}

/**
 * Update feedback status (HR action)
 */
export async function updateFeedbackStatus(
  feedbackId: string,
  status: FeedbackStatus,
  reviewedBy: string,
  hrResponse?: string,
  hrNotes?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('📝 [FEEDBACK] Updating feedback status:', {
      feedbackId,
      status,
      reviewedBy
    })

    interface UpdateData {
      status: FeedbackStatus
      reviewed_by: string
      reviewed_at: string
      hr_response?: string
      hr_notes?: string
      resolved_by?: string
      resolved_at?: string
    }

    const updateData: UpdateData = {
      status,
      reviewed_by: reviewedBy,
      reviewed_at: new Date().toISOString()
    }

    if (hrResponse) {
      updateData.hr_response = hrResponse
    }

    if (hrNotes) {
      updateData.hr_notes = hrNotes
    }

    if (status === 'resolved') {
      updateData.resolved_by = reviewedBy
      updateData.resolved_at = new Date().toISOString()
    }

    const { error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .update(updateData)
      .eq('id', feedbackId)

    if (error) {
      console.error('❌ [FEEDBACK] Failed to update feedback status:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [FEEDBACK] Feedback status updated successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error updating feedback status:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Add comment to feedback (HR internal discussion)
 */
export async function addFeedbackComment(
  feedbackId: string,
  commenterId: string,
  comment: string,
  isInternal: boolean = true
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('💬 [FEEDBACK] Adding comment to feedback:', feedbackId)

    const { error } = await supabaseAdmin
      .from('appy_feedback_comments')
      .insert({
        feedback_id: feedbackId,
        commenter_id: commenterId,
        comment,
        is_internal: isInternal
      })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to add comment:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [FEEDBACK] Comment added successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error adding comment:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Get feedback comments
 */
export async function getFeedbackComments(feedbackId: string): Promise<FeedbackComment[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('appy_feedback_comments')
      .select(`
        *,
        commenter:commenter_id(full_name)
      `)
      .eq('feedback_id', feedbackId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch comments:', error)
      return []
    }

    interface CommentData {
      id: string
      feedback_id: string
      commenter_id: string
      commenter?: {
        full_name: string
      } | null
      comment: string
      is_internal: boolean
      created_at: string
      updated_at: string
    }

    return ((data || []) as CommentData[]).map((comment) => ({
      id: comment.id,
      feedbackId: comment.feedback_id,
      commenterId: comment.commenter_id,
      commenterName: comment.commenter?.full_name || 'Unknown',
      comment: comment.comment,
      isInternal: comment.is_internal,
      createdAt: comment.created_at,
      updatedAt: comment.updated_at
    }))

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching comments:', error)
    return []
  }
}

/**
 * Get feedback statistics for HR dashboard
 */
export async function getFeedbackStatistics(
  startDate?: string,
  endDate?: string
): Promise<FeedbackStatistics | null> {
  try {
    debug.log('📊 [FEEDBACK] Fetching feedback statistics with date range:', { startDate, endDate })

    // Build the base query
    let query = supabaseAdmin
      .from('appy_employee_feedback')
      .select('*')

    // Apply date filters if provided
    if (startDate) {
      query = query.gte('created_at', startDate)
    }
    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data: feedbackData, error } = await query

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch feedback for statistics:', error)
      return null
    }

    // Calculate statistics from the data
    const totalFeedback = feedbackData?.length || 0
    const pendingCount = feedbackData?.filter(f => f.status === 'pending').length || 0
    const underReviewCount = feedbackData?.filter(f => f.status === 'under_review').length || 0
    const resolvedCount = feedbackData?.filter(f => ['resolved', 'closed'].includes(f.status)).length || 0

    // Group by type
    const byType: Record<string, number> = {}
    feedbackData?.forEach(f => {
      if (f.feedback_type) {
        byType[f.feedback_type] = (byType[f.feedback_type] || 0) + 1
      }
    })

    // Group by priority
    const byPriority: Record<string, number> = {}
    feedbackData?.forEach(f => {
      if (f.priority) {
        byPriority[f.priority] = (byPriority[f.priority] || 0) + 1
      }
    })

    // Calculate average resolution days
    const resolvedFeedback = feedbackData?.filter(f => f.resolved_at) || []
    const avgResolutionDays = resolvedFeedback.length > 0
      ? resolvedFeedback.reduce((sum, f) => {
          const days = Math.floor((new Date(f.resolved_at).getTime() - new Date(f.created_at).getTime()) / (1000 * 60 * 60 * 24))
          return sum + days
        }, 0) / resolvedFeedback.length
      : 0

    const statistics = {
      totalFeedback,
      pendingCount,
      underReviewCount,
      resolvedCount,
      byType,
      byPriority,
      avgResolutionDays: Math.round(avgResolutionDays * 100) / 100
    }

    debug.log('✅ [FEEDBACK] Statistics calculated successfully:', statistics)
    return statistics

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching statistics:', error)
    return null
  }
}

/**
 * Helper function to map database feedback to TypeScript type
 */
interface FeedbackDBData {
  id: string
  submitter_id: string
  submitter?: {
    full_name: string
    email: string
  } | null
  target_employee_id: string | null
  target_employee?: {
    full_name: string
    email: string
  } | null
  feedback_type: string
  category: string | null
  subject: string
  message: string
  is_anonymous: boolean
  priority: string
  status: FeedbackStatus
  hr_response: string | null
  hr_notes: string | null
  reviewed_by: string | null
  reviewed_by_manager?: {
    full_name: string
  } | null
  reviewed_at: string | null
  resolution_summary: string | null
  resolved_by: string | null
  resolved_by_manager?: {
    full_name: string
  } | null
  resolved_at: string | null
  requires_followup: boolean
  followup_date: string | null
  followup_notes: string | null
  created_at: string
  updated_at: string
}

function mapFeedbackFromDB(data: FeedbackDBData): EmployeeFeedback {
  return {
    id: data.id,
    submitterId: data.submitter_id,
    submitterName: data.is_anonymous ? 'Anonymous' : data.submitter?.full_name,
    targetEmployeeId: data.target_employee_id,
    targetEmployeeName: data.target_employee?.full_name || null,
    feedbackType: data.feedback_type as FeedbackType,
    category: data.category as FeedbackCategory | null,
    subject: data.subject,
    message: data.message,
    isAnonymous: data.is_anonymous,
    priority: data.priority as FeedbackPriority,
    status: data.status,
    hrResponse: data.hr_response,
    hrNotes: data.hr_notes,
    reviewedBy: data.reviewed_by,
    reviewedAt: data.reviewed_at,
    resolutionSummary: data.resolution_summary,
    resolvedBy: data.resolved_by,
    resolvedAt: data.resolved_at,
    requiresFollowup: data.requires_followup,
    followupDate: data.followup_date,
    followupNotes: data.followup_notes,
    createdAt: data.created_at,
    updatedAt: data.updated_at
  }
}

debug.log('📝 Feedback data service initialized')
