import type { Manager } from '../types'
import { db } from '../db'
import { generateUserId } from '../utils'

export async function getManagers(): Promise<Manager[]> {
  try {
    const managers = await db.getManagers()
    return managers.map(manager => ({
      id: manager.user_id,
      fullName: manager.full_name,
      email: manager.email,
      role: 'manager' as const,
      departmentId: manager.department_id
    }))
  } catch (error) {
    console.error('Failed to fetch managers from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveManager(managerData: {
  fullName: string
  email: string
  role: 'manager' | 'admin' | 'hr-admin' | 'super-admin' | 'senior-manager' | 'accountant'
  departmentId?: string
}): Promise<Manager> {
  try {
    // Generate a unique user ID for Clerk integration
    const userId = generateUserId()
    
    const dbManager = await db.createManager(
      userId,
      managerData.fullName,
      managerData.email,
      managerData.departmentId
    )
    
    return {
      id: dbManager.user_id,
      fullName: dbManager.full_name,
      email: dbManager.email,
      role: managerData.role,
      departmentId: dbManager.department_id
    }
  } catch (error) {
    console.error('Failed to save manager:', error)
    throw new Error('Failed to create manager')
  }
}