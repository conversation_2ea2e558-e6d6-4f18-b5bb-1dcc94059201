"use server"

import { supabaseAdmin } from '../supabase'
import type { HierarchyNode } from '../types'

export async function getHierarchyData(): Promise<HierarchyNode[]> {
  try {
    // Get all managers with their reporting relationships
    const { data: managers, error: managersError } = await supabaseAdmin
      .from('appy_managers')
      .select(`
        user_id,
        full_name,
        role,
        manager_id,
        active,
        department_id,
        departments:department_id (
          name
        )
      `)
      .eq('active', true)
      .order('full_name')

    if (managersError) {
      console.error('Error fetching managers:', managersError)
      return []
    }

    // Get all employees with their managers
    const { data: employees, error: employeesError } = await supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        manager_id,
        department_id,
        departments:department_id (
          name
        )
      `)
      .eq('active', true)
      .order('full_name')

    if (employeesError) {
      console.error('Error fetching employees:', employeesError)
      return []
    }

    // Build hierarchy tree
    const hierarchyMap = new Map<string, HierarchyNode>()
    
    // First, create nodes for all managers
    managers.forEach(manager => {
      hierarchyMap.set(manager.user_id, {
        id: manager.user_id,
        name: manager.full_name,
        role: manager.role,
        department: (manager.departments as any)?.name || 'Unknown',
        type: 'manager',
        children: [],
        parentId: manager.manager_id
      })
    })

    // Add employees as leaf nodes
    employees.forEach(employee => {
      if (employee.manager_id) {
        const employeeNode: HierarchyNode = {
          id: employee.id,
          name: employee.full_name,
          role: 'employee',
          department: (employee.departments as any)?.name || 'Unknown',
          type: 'employee',
          children: [],
          parentId: employee.manager_id
        }
        
        // Add to manager's children
        const parentManager = hierarchyMap.get(employee.manager_id)
        if (parentManager) {
          parentManager.children.push(employeeNode)
        }
      }
    })

    // Build the tree structure by connecting managers to their parent managers
    const rootNodes: HierarchyNode[] = []
    
    hierarchyMap.forEach(node => {
      if (node.parentId && hierarchyMap.has(node.parentId)) {
        // This is a manager with a parent manager
        const parent = hierarchyMap.get(node.parentId)!
        parent.children.push(node)
      } else {
        // This is a root node (no parent manager)
        rootNodes.push(node)
      }
    })

    return rootNodes
  } catch (error) {
    console.error('Failed to fetch hierarchy data:', error)
    return []
  }
}

export async function getDepartmentHierarchy(departmentId: string): Promise<HierarchyNode[]> {
  try {
    const { data: managers, error } = await supabaseAdmin
      .from('appy_managers')
      .select(`
        user_id,
        full_name,
        role,
        manager_id,
        active,
        departments:department_id (
          name
        )
      `)
      .eq('department_id', departmentId)
      .eq('active', true)
      .order('full_name')

    if (error) {
      console.error('Error fetching department hierarchy:', error)
      return []
    }

    // Build department-specific hierarchy
    const hierarchyMap = new Map<string, HierarchyNode>()
    
    managers.forEach(manager => {
      hierarchyMap.set(manager.user_id, {
        id: manager.user_id,
        name: manager.full_name,
        role: manager.role,
        department: (manager.departments as any)?.name || 'Unknown',
        type: 'manager',
        children: [],
        parentId: manager.manager_id
      })
    })

    // Connect parent-child relationships within the department
    const rootNodes: HierarchyNode[] = []
    
    hierarchyMap.forEach(node => {
      if (node.parentId && hierarchyMap.has(node.parentId)) {
        const parent = hierarchyMap.get(node.parentId)!
        parent.children.push(node)
      } else {
        rootNodes.push(node)
      }
    })

    return rootNodes
  } catch (error) {
    console.error('Failed to fetch department hierarchy:', error)
    return []
  }
}