import { supabaseAdmin } from '../../supabase-admin'
import { debug } from '../../debug'
import type { Employee, EmployeeWithDepartment } from '../core/types'

// Employees
export async function getEmployees(): Promise<EmployeeWithDepartment[]> {
  debug.log('🔍 [DEBUG] Starting getEmployees() function with multi-manager support')

  // Fetch employees with departments
  const { data: employees, error: employeesError } = await supabaseAdmin.from('appy_employees')
    .select(`
      *,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('active', true)
    .order('full_name')

  if (employeesError) {
    throw new Error(employeesError.message)
  }

  debug.log('👥 [DEBUG] Fetched employees:', employees?.map(emp => ({
    id: emp.id,
    full_name: emp.full_name,
    manager_id: emp.manager_id
  })))

  // Fetch all employee-manager relationships from the junction table with department info
  const { data: employeeManagers, error: emError } = await supabaseAdmin.from('appy_employee_managers')
    .select(`
      employee_id,
      manager_id,
      is_primary,
      assigned_at,
      appy_managers:manager_id (
        user_id,
        full_name,
        department_id,
        appy_departments:department_id (
          id,
          name
        )
      )
    `)
    .order('is_primary', { ascending: false }) // Primary managers first

  if (emError) {
    throw new Error(emError.message)
  }

  debug.log('👨‍💼 [DEBUG] Fetched employee-manager relationships:', employeeManagers?.length || 0)

  // Group managers by employee with department information
  const employeeManagersMap = new Map<string, Array<{
    managerId: string
    managerName: string
    isPrimary: boolean
    assignedAt: string
    departmentId: string | null
    departmentName: string | null
  }>>()

  ;(employeeManagers || []).forEach(em => {
    if (!employeeManagersMap.has(em.employee_id)) {
      employeeManagersMap.set(em.employee_id, [])
    }
    
    const manager = em.appy_managers as any
    const managerDepartment = manager?.appy_departments as any

    employeeManagersMap.get(em.employee_id)!.push({
      managerId: em.manager_id,
      managerName: manager?.full_name || 'Unknown Manager',
      isPrimary: em.is_primary || false,
      assignedAt: em.assigned_at,
      departmentId: manager?.department_id || null,
      departmentName: managerDepartment?.name || null
    })
  })

  debug.log('🗺️ [DEBUG] Employee-managers map:', Array.from(employeeManagersMap.entries()).slice(0, 3))

  // Transform the data to match expected format with multi-manager support
  const result = (employees || []).map(employee => {
    const managers = employeeManagersMap.get(employee.id) || []
    const primaryManager = managers.find(m => m.isPrimary)
    
    // For backward compatibility, set legacy fields to primary manager
    const legacyManagerName = primaryManager?.managerName || null

    debug.log(`🔗 [DEBUG] Employee ${employee.full_name}: ${managers.length} managers, primary: ${primaryManager?.managerName || 'none'}`)

    return {
      ...employee,
      department_name: employee.appy_departments?.name || '',
      manager_name: legacyManagerName, // Legacy field
      managers: managers, // New multi-manager field with department info
      manager_ids: managers.map(m => m.managerId) // New field for quick access
    }
  })

  debug.log('✅ [DEBUG] Final employee data with multi-manager support:', result.map(emp => ({
    full_name: emp.full_name,
    manager_count: emp.managers?.length || 0,
    primary_manager: emp.managers?.find((m: any) => m.isPrimary)?.managerName || 'none'
  })))

  return result
}

export async function getEmployeeById(id: string): Promise<EmployeeWithDepartment | null> {
  // Fetch employee with department
  const { data: employee, error: employeeError } = await supabaseAdmin.from('appy_employees')
    .select(`
      *,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('id', id)
    .single()
  
  if (employeeError) {
    if (employeeError.code === 'PGRST116') {
      return null // Not found
    }
    throw new Error(employeeError.message)
  }

  // Fetch all managers for this employee from the junction table with department info
  const { data: employeeManagers, error: emError } = await supabaseAdmin.from('appy_employee_managers')
    .select(`
      manager_id,
      is_primary,
      assigned_at,
      appy_managers:manager_id (
        user_id,
        full_name,
        department_id,
        appy_departments:department_id (
          id,
          name
        )
      )
    `)
    .eq('employee_id', id)
    .order('is_primary', { ascending: false }) // Primary managers first

  if (emError) {
    debug.log('⚠️ [DEBUG] Error fetching employee managers:', emError)
  }

  // Transform manager data with department information
  const managers = (employeeManagers || []).map(em => {
    const manager = em.appy_managers as any
    const managerDepartment = manager?.appy_departments as any

    return {
      managerId: em.manager_id,
      managerName: manager?.full_name || 'Unknown Manager',
      isPrimary: em.is_primary || false,
      assignedAt: em.assigned_at,
      departmentId: manager?.department_id || null,
      departmentName: managerDepartment?.name || null
    }
  })

  const primaryManager = managers.find(m => m.isPrimary)
  
  return {
    ...employee,
    department_name: employee.appy_departments?.name || '',
    manager_name: primaryManager?.managerName || null, // Legacy field
    managers: managers, // New multi-manager field with department info
    manager_ids: managers.map(m => m.managerId) // New field for quick access
  }
}

export async function getEmployeeByClerkId(clerkId: string): Promise<EmployeeWithDepartment | null> {
  debug.log('🔍 Getting employee by Clerk ID:', clerkId)

  // First try to find by manager_id (if they are a manager)
  const { data: managerEmployee, error: managerError } = await supabaseAdmin.from('appy_employees')
    .select(`
      *,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('manager_id', clerkId)
    .single()

  if (managerEmployee && !managerError) {
    debug.log('✅ Found employee by manager_id:', managerEmployee.full_name)
    return {
      ...managerEmployee,
      departmentName: managerEmployee.appy_departments?.name || null
    }
  }

  // If not found as manager, try to find by email through managers table
  const { data: manager, error: managerLookupError } = await supabaseAdmin.from('appy_managers')
    .select('email')
    .eq('user_id', clerkId)
    .single()

  if (manager && !managerLookupError) {
    const { data: employeeByEmail, error: emailError } = await supabaseAdmin.from('appy_employees')
      .select(`
        *,
        appy_departments:department_id (
          id,
          name
        )
      `)
      .eq('email', manager.email)
      .single()

    if (employeeByEmail && !emailError) {
      debug.log('✅ Found employee by email:', employeeByEmail.full_name)
      return {
        ...employeeByEmail,
        departmentName: employeeByEmail.appy_departments?.name || null
      }
    }
  }

  debug.log('❌ Employee not found for Clerk ID:', clerkId)
  return null
}

export async function createEmployee(employeeData: {
  fullName: string
  firstName?: string
  lastName?: string
  email: string
  role?: string
  bio?: string
  linkedinUrl?: string
  twitterUrl?: string
  telegramUrl?: string
  compensation: number
  rate: 'hourly' | 'monthly'
  departmentId: string
  managerId?: string
}): Promise<Employee> {
  if (process.env.ENABLE_DEBUG_LOGS === 'true') {
    console.log('🔧 [DEBUG] createEmployee called with:', employeeData)
  }

  // Check email uniqueness
  const emailIsUnique = await isEmailUnique(employeeData.email)
  if (!emailIsUnique) {
    throw new Error('An employee with this email address already exists')
  }

  const { data, error } = await supabaseAdmin.from('appy_employees')
    .insert({
      full_name: employeeData.fullName,
      first_name: employeeData.firstName || null,
      last_name: employeeData.lastName || null,
      email: employeeData.email,
      role: employeeData.role || null,
      bio: employeeData.bio || null,
      linkedin_url: employeeData.linkedinUrl || null,
      twitter_url: employeeData.twitterUrl || null,
      telegram_url: employeeData.telegramUrl || null,
      compensation: employeeData.compensation,
      rate: employeeData.rate,
      department_id: employeeData.departmentId,
      manager_id: employeeData.managerId || null
    })
    .select()
    .single()

  if (error) {
    console.error('❌ [ERROR] Database insert failed:', error)

    // Provide more specific error messages
    if (error.code === '23505' && error.message.includes('unique_employee_email')) {
      throw new Error('An employee with this email address already exists')
    } else if (error.code === '23503') {
      throw new Error('Invalid department or manager reference')
    } else {
      throw new Error(`Database error: ${error.message}`)
    }
  }

  if (process.env.ENABLE_DEBUG_LOGS === 'true') {
    console.log('✅ [DEBUG] Employee created successfully:', data.id)
  }
  return data
}

// Helper function to check if email is unique (excluding current employee)
export async function isEmailUnique(email: string, excludeId?: string): Promise<boolean> {
  const query = supabaseAdmin.from('appy_employees')
    .select('id')
    .eq('email', email)
    .limit(1)

  if (excludeId) {
    query.neq('id', excludeId)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error checking email uniqueness:', error)
    return false
  }

  return data.length === 0
}

export async function updateEmployee(id: string, employeeData: {
  fullName?: string
  firstName?: string
  lastName?: string
  email?: string
  role?: string
  bio?: string
  linkedinUrl?: string
  twitterUrl?: string
  telegramUrl?: string
  compensation?: number
  rate?: 'hourly' | 'monthly'
  departmentId?: string
  managerId?: string
  active?: boolean
}): Promise<Employee> {
  if (process.env.ENABLE_DEBUG_LOGS === 'true') {
    console.log('🔧 [DEBUG] updateEmployee called with:', { id, employeeData })
  }

  // Check email uniqueness if email is being updated
  if (employeeData.email !== undefined) {
    const emailIsUnique = await isEmailUnique(employeeData.email, id)
    if (!emailIsUnique) {
      throw new Error('An employee with this email address already exists')
    }
  }

  const updateData: any = {}

  if (employeeData.fullName !== undefined) updateData.full_name = employeeData.fullName
  if (employeeData.firstName !== undefined) updateData.first_name = employeeData.firstName
  if (employeeData.lastName !== undefined) updateData.last_name = employeeData.lastName
  if (employeeData.email !== undefined) updateData.email = employeeData.email
  if (employeeData.role !== undefined) updateData.role = employeeData.role
  if (employeeData.bio !== undefined) updateData.bio = employeeData.bio
  if (employeeData.linkedinUrl !== undefined) updateData.linkedin_url = employeeData.linkedinUrl
  if (employeeData.twitterUrl !== undefined) updateData.twitter_url = employeeData.twitterUrl
  if (employeeData.telegramUrl !== undefined) updateData.telegram_url = employeeData.telegramUrl
  if (employeeData.compensation !== undefined) updateData.compensation = employeeData.compensation
  if (employeeData.rate !== undefined) updateData.rate = employeeData.rate
  if (employeeData.departmentId !== undefined) updateData.department_id = employeeData.departmentId
  if (employeeData.managerId !== undefined) updateData.manager_id = employeeData.managerId
  if (employeeData.active !== undefined) updateData.active = employeeData.active

  if (Object.keys(updateData).length === 0) {
    throw new Error('No fields to update')
  }

  if (process.env.ENABLE_DEBUG_LOGS === 'true') {
    console.log('🔄 [DEBUG] Executing update with data:', updateData)
  }

  const { data, error } = await supabaseAdmin.from('appy_employees')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('❌ [ERROR] Database update failed:', error)

    // Provide more specific error messages
    if (error.code === '23505' && error.message.includes('unique_employee_email')) {
      throw new Error('An employee with this email address already exists')
    } else if (error.code === '23503') {
      throw new Error('Invalid department or manager reference')
    } else {
      throw new Error(`Database error: ${error.message}`)
    }
  }

  if (process.env.ENABLE_DEBUG_LOGS === 'true') {
    console.log('✅ [DEBUG] Employee updated successfully:', data.id)
  }
  return data
}

export async function softDeleteEmployee(id: string): Promise<void> {
  const { error } = await supabaseAdmin.from('appy_employees')
    .update({ active: false })
    .eq('id', id)

  if (error) {
    throw new Error(error.message)
  }
}

export async function hardDeleteEmployee(id: string): Promise<void> {
  console.log('🗑️ [DEBUG] Hard deleting employee:', id)

  // First, delete related records to avoid foreign key constraints
  // Delete employee-manager relationships
  await supabaseAdmin.from('appy_employee_managers')
    .delete()
    .eq('employee_id', id)

  // Delete employee KPIs
  await supabaseAdmin.from('appy_employee_kpis')
    .delete()
    .eq('employee_id', id)

  // Delete appraisals (this will cascade to approval workflows)
  await supabaseAdmin.from('appy_appraisals')
    .delete()
    .eq('employee_id', id)

  // Finally, delete the employee record
  const { error } = await supabaseAdmin.from('appy_employees')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('❌ [ERROR] Failed to hard delete employee:', error)
    throw new Error(error.message)
  }

  console.log('✅ [DEBUG] Employee hard deleted successfully:', id)
}