import { supabaseAdmin } from '../../supabase'

// Employee Feedback System Functions
export async function createFeedback(feedbackData: {
  submitterId: string
  targetEmployeeId?: string
  feedbackType: 'complaint' | 'suggestion' | 'recognition' | 'concern' | 'initiative' | 'general'
  category?: 'performance' | 'behavior' | 'communication' | 'teamwork' | 'leadership' | 'process' | 'other'
  subject: string
  message: string
  isAnonymous?: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
}): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_employee_feedback')
    .insert({
      submitter_id: feedbackData.submitterId,
      target_employee_id: feedbackData.targetEmployeeId || null,
      feedback_type: feedbackData.feedbackType,
      category: feedbackData.category || null,
      subject: feedbackData.subject,
      message: feedbackData.message,
      is_anonymous: feedbackData.isAnonymous || false,
      priority: feedbackData.priority || 'medium',
      status: 'pending'
    })
    .select()
    .single()

  if (error) {
    console.error('Failed to create feedback:', error)
    throw new Error(error.message)
  }

  return data
}

export async function getFeedback(filters?: {
  submitterId?: string
  targetEmployeeId?: string
  status?: string
  feedbackType?: string
  priority?: string
  limit?: number
}): Promise<any[]> {
  let query = supabaseAdmin
    .from('appy_employee_feedback')
    .select(`
      *,
      submitter:appy_employees!submitter_id (
        id,
        full_name,
        email
      ),
      target:appy_employees!target_employee_id (
        id,
        full_name,
        email
      ),
      reviewer:appy_managers!reviewed_by (
        user_id,
        full_name,
        email
      )
    `)
    .order('created_at', { ascending: false })

  if (filters?.submitterId) {
    query = query.eq('submitter_id', filters.submitterId)
  }

  if (filters?.targetEmployeeId) {
    query = query.eq('target_employee_id', filters.targetEmployeeId)
  }

  if (filters?.status) {
    query = query.eq('status', filters.status)
  }

  if (filters?.feedbackType) {
    query = query.eq('feedback_type', filters.feedbackType)
  }

  if (filters?.priority) {
    query = query.eq('priority', filters.priority)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  const { data, error } = await query

  if (error) {
    console.error('Failed to fetch feedback:', error)
    throw new Error(error.message)
  }

  return data || []
}

export async function getFeedbackById(feedbackId: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_employee_feedback')
    .select(`
      *,
      submitter:appy_employees!submitter_id (
        id,
        full_name,
        email
      ),
      target:appy_employees!target_employee_id (
        id,
        full_name,
        email
      ),
      reviewer:appy_managers!reviewed_by (
        user_id,
        full_name,
        email
      ),
      resolver:appy_managers!resolved_by (
        user_id,
        full_name,
        email
      )
    `)
    .eq('id', feedbackId)
    .single()

  if (error) {
    console.error('Failed to fetch feedback by ID:', error)
    throw new Error(error.message)
  }

  return data
}

export async function updateFeedbackStatus(feedbackId: string, updates: {
  status?: 'pending' | 'under_review' | 'investigating' | 'resolved' | 'closed' | 'escalated'
  reviewedBy?: string
  hrResponse?: string
  hrNotes?: string
  resolutionSummary?: string
  resolvedBy?: string
  requiresFollowup?: boolean
  followupDate?: string
  followupNotes?: string
}): Promise<any> {
  const updateData: any = {}

  if (updates.status) updateData.status = updates.status
  if (updates.reviewedBy) updateData.reviewed_by = updates.reviewedBy
  if (updates.hrResponse) updateData.hr_response = updates.hrResponse
  if (updates.hrNotes) updateData.hr_notes = updates.hrNotes
  if (updates.resolutionSummary) updateData.resolution_summary = updates.resolutionSummary
  if (updates.resolvedBy) updateData.resolved_by = updates.resolvedBy
  if (updates.requiresFollowup !== undefined) updateData.requires_followup = updates.requiresFollowup
  if (updates.followupDate) updateData.followup_date = updates.followupDate
  if (updates.followupNotes) updateData.followup_notes = updates.followupNotes

  if (updates.status === 'under_review' && updates.reviewedBy) {
    updateData.reviewed_at = new Date().toISOString()
  }

  if (updates.status === 'resolved' && updates.resolvedBy) {
    updateData.resolved_at = new Date().toISOString()
  }

  const { data, error } = await supabaseAdmin
    .from('appy_employee_feedback')
    .update(updateData)
    .eq('id', feedbackId)
    .select()
    .single()

  if (error) {
    console.error('Failed to update feedback status:', error)
    throw new Error(error.message)
  }

  return data
}

export async function addFeedbackComment(commentData: {
  feedbackId: string
  commenterId: string
  comment: string
  isInternal?: boolean
}): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_feedback_comments')
    .insert({
      feedback_id: commentData.feedbackId,
      commenter_id: commentData.commenterId,
      comment: commentData.comment,
      is_internal: commentData.isInternal || true
    })
    .select()
    .single()

  if (error) {
    console.error('Failed to add feedback comment:', error)
    throw new Error(error.message)
  }

  return data
}

export async function getFeedbackComments(feedbackId: string): Promise<any[]> {
  const { data, error } = await supabaseAdmin
    .from('appy_feedback_comments')
    .select(`
      *,
      commenter:appy_managers!commenter_id (
        user_id,
        full_name,
        email
      )
    `)
    .eq('feedback_id', feedbackId)
    .order('created_at', { ascending: true })

  if (error) {
    console.error('Failed to fetch feedback comments:', error)
    throw new Error(error.message)
  }

  return data || []
}

export async function getFeedbackStatistics(startDate?: string, endDate?: string): Promise<any> {
  console.log('📊 [FEEDBACK] Fetching feedback statistics with date range:', { startDate, endDate })

  try {
    // Build the base query
    let query = supabaseAdmin
      .from('appy_employee_feedback')
      .select('*')

    // Apply date filters if provided
    if (startDate) {
      query = query.gte('created_at', startDate)
    }
    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    const { data: feedbackData, error } = await query

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch feedback for statistics:', error)
      throw new Error(error.message)
    }

    // Calculate statistics from the data
    const totalFeedback = feedbackData?.length || 0
    const pendingCount = feedbackData?.filter(f => f.status === 'pending').length || 0
    const underReviewCount = feedbackData?.filter(f => f.status === 'under_review').length || 0
    const resolvedCount = feedbackData?.filter(f => ['resolved', 'closed'].includes(f.status)).length || 0

    // Group by type
    const byType: Record<string, number> = {}
    feedbackData?.forEach(f => {
      if (f.feedback_type) {
        byType[f.feedback_type] = (byType[f.feedback_type] || 0) + 1
      }
    })

    // Group by priority
    const byPriority: Record<string, number> = {}
    feedbackData?.forEach(f => {
      if (f.priority) {
        byPriority[f.priority] = (byPriority[f.priority] || 0) + 1
      }
    })

    // Calculate average resolution days
    const resolvedFeedback = feedbackData?.filter(f => f.resolved_at) || []
    const avgResolutionDays = resolvedFeedback.length > 0
      ? resolvedFeedback.reduce((sum, f) => {
          const days = Math.floor((new Date(f.resolved_at).getTime() - new Date(f.created_at).getTime()) / (1000 * 60 * 60 * 24))
          return sum + days
        }, 0) / resolvedFeedback.length
      : 0

    const statistics = {
      total_feedback: totalFeedback,
      pending_count: pendingCount,
      under_review_count: underReviewCount,
      resolved_count: resolvedCount,
      by_type: byType,
      by_priority: byPriority,
      avg_resolution_days: Math.round(avgResolutionDays * 100) / 100
    }

    console.log('✅ [FEEDBACK] Statistics calculated successfully:', statistics)
    return statistics

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error calculating statistics:', error)
    return {
      total_feedback: 0,
      pending_count: 0,
      under_review_count: 0,
      resolved_count: 0,
      by_type: {},
      by_priority: {},
      avg_resolution_days: 0
    }
  }
}

export async function getPendingFeedbackForHR(): Promise<any[]> {
  console.log('🔍 [FEEDBACK] Fetching pending feedback for HR')

  try {
    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .select(`
        id,
        submitter_id,
        target_employee_id,
        feedback_type,
        subject,
        priority,
        status,
        created_at,
        submitter:appy_employees!submitter_id (
          full_name
        ),
        target:appy_employees!target_employee_id (
          full_name
        )
      `)
      .in('status', ['pending', 'under_review'])
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch pending feedback:', error)
      throw new Error(error.message)
    }

    // Transform the data to match expected format
    const transformedData = (data || []).map(feedback => {
      const daysPending = Math.floor(
        (new Date().getTime() - new Date(feedback.created_at).getTime()) / (1000 * 60 * 60 * 24)
      )

      return {
        feedback_id: feedback.id,
        submitter_name: (feedback.submitter as any)?.full_name || 'Anonymous',
        target_name: (feedback.target as any)?.full_name || null,
        feedback_type: feedback.feedback_type,
        subject: feedback.subject,
        priority: feedback.priority,
        created_at: feedback.created_at,
        days_pending: daysPending
      }
    })

    console.log(`✅ [FEEDBACK] Found ${transformedData.length} pending feedback items`)
    return transformedData

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching pending feedback:', error)
    return []
  }
}