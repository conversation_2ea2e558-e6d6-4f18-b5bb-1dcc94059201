import type { Database } from '../../supabase'
import type { EmployeeManager } from '../../types'

// Type aliases for better readability
export type Department = Database['public']['Tables']['appy_departments']['Row']
export type Employee = Database['public']['Tables']['appy_employees']['Row']
export type Manager = Database['public']['Tables']['appy_managers']['Row']
export type AppraisalPeriod = Database['public']['Tables']['appy_appraisal_periods']['Row']
export type Appraisal = Database['public']['Tables']['appy_appraisals']['Row']

// Extended types with joined data
export interface EmployeeWithDepartment extends Employee {
  department_name: string
  manager_name: string | null // Legacy field for backward compatibility
  managers?: Array<{
    managerId: string
    managerName: string
    isPrimary: boolean
    assignedAt: string
  }>
  manager_ids?: string[] // Array of all manager IDs for quick access
}

export interface AppraisalWithDetails extends Appraisal {
  employee_name: string
  manager_name: string
  department_name: string
}

// Query result type
export interface QueryResult<T> {
  rows: T[]
  rowCount: number
}

// Re-export EmployeeManager from types
export type { EmployeeManager }