"use server"

import { revalidatePath } from "next/cache"
import { getPeriods, invalidatePerformanceStatsCache } from "../data/index"
import { db } from "../db"
import {
  validateAppraisalSubmission,
  appraisalFormSchema
} from "../schemas"
import type { AppraisalDetails } from "../types"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError,
  ValidationError
} from "./shared"
import { debug } from "../debug"

export async function saveAppraisalDraftAction(draft: Partial<AppraisalDetails>) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting (more generous for autosave)
    if (!checkRateLimit(session.userId, 'appraisal-draft', 30, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate draft data (less strict than submission)
    const validatedData = appraisalFormSchema.partial().parse(draft)

    // Get current period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      throw new ValidationError("No active appraisal period found")
    }

    // Ensure we have required fields for draft
    if (!draft.employeeId) {
      throw new ValidationError("Employee ID is required")
    }

    // Log the action (less verbose for drafts)
    console.log(`Saving draft for user ${session.userId}`, {
      employeeId: draft.employeeId,
      hasQ1: !!draft.q1,
      hasQ2: draft.q2 !== undefined,
      hasQ3: !!draft.q3,
      hasQ4: !!draft.q4,
      hasQ5: !!draft.q5,
    })

    // Save draft to database
    try {
      // Ensure manager exists in appy_managers table
      await db.ensureManagerExists(session.userId, session.fullName, session.email)
      
      await db.saveAppraisalDraft({
        periodId: currentPeriod.id,
        employeeId: draft.employeeId,
        managerId: session.userId,
        question1: draft.q1 || null,
        question2: draft.q2 !== undefined ? String(draft.q2) : null,
        question3: draft.q3 || null,
        question4: draft.q4 || null,
        question5: draft.q5 || null,
        paymentStatus: draft.paymentStatus || null,
        // New fields
        keyContributions: draft.keyContributions || null,
        extraInitiatives: draft.extraInitiatives || null,
        performanceLacking: draft.performanceLacking || null,
        disciplineRating: draft.disciplineRating || null,
        disciplineComment: draft.disciplineComment || null,
        daysOffTaken: draft.daysOffTaken || null,
        impactRating: draft.impactRating || null,
        impactComment: draft.impactComment || null,
        qualityRating: draft.qualityRating || null,
        qualityComment: draft.qualityComment || null,
        collaborationRating: draft.collaborationRating || null,
        collaborationComment: draft.collaborationComment || null,
        skillGrowthRating: draft.skillGrowthRating || null,
        skillGrowthComment: draft.skillGrowthComment || null,
        readinessPromotion: draft.readinessPromotion || null,
        readinessComment: draft.readinessComment || null,
        compensationRecommendation: draft.compensationRecommendation || null,
      })

      console.log("Draft saved successfully to database.")
    } catch (dbError) {
      console.error("Database error during draft save:", dbError)
      // Don't throw error for draft saves - just log and continue
      console.log("Draft save failed, but continuing...")
    }

    // Ensure the response is serializable
    return { success: true as const, message: "Draft saved successfully." }
  } catch (error) {
    console.error('❌ [DEBUG] Draft save error:', error)
    return handleServerActionError(error)
  }
}

export async function submitAppraisalAction(appraisal: Partial<AppraisalDetails>) {
  try {
    console.log('🚀 [DEBUG] Starting appraisal submission')

    // Check if the input data is serializable
    try {
      JSON.stringify(appraisal)
      console.log('✅ [DEBUG] Input appraisal data is serializable')
    } catch (serializationError) {
      console.error('❌ [DEBUG] Input appraisal data is not serializable:', serializationError)
      throw new ValidationError('Invalid form data: contains non-serializable values')
    }

    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for submissions)
    if (!checkRateLimit(session.userId, 'appraisal-submit', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate submission data (strict validation)
    const submissionData = {
      ...appraisal,
      managerId: session.userId, // Set managerId from authenticated session
      status: 'submitted' as const,
    }

    try {
      console.log('📋 [DEBUG] Submission data before validation:', JSON.stringify(submissionData, null, 2))
    } catch (jsonError) {
      console.log('📋 [DEBUG] Submission data (raw):', submissionData)
      console.log('📋 [DEBUG] JSON stringify error:', jsonError)
    }

    let validatedData
    try {
      validatedData = validateAppraisalSubmission(submissionData)
      console.log('✅ [DEBUG] Validation successful')
    } catch (validationError) {
      console.error('❌ [DEBUG] Validation failed:', validationError)
      throw validationError
    }

    // Additional business logic validation
    if (!validatedData.employeeId) {
      throw new ValidationError("Employee ID is required")
    }

    // Log the action
    await logUserAction('appraisal:submit', {
      employeeId: validatedData.employeeId,
      managerId: session.userId,
      submittedAt: new Date().toISOString()
    })

    console.log(`Submitting appraisal for employee ${validatedData.employeeId}`)

    // Get current period (in real app, this would be more sophisticated)
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      throw new ValidationError("No active appraisal period found")
    }

    // Save or update the appraisal in the database
    try {
      // Ensure manager exists in appy_managers table
      await db.ensureManagerExists(session.userId, session.fullName, session.email)
      
      // First, save as draft to ensure the record exists
      await db.saveAppraisalDraft({
        periodId: currentPeriod.id,
        employeeId: validatedData.employeeId,
        managerId: session.userId,
        question1: validatedData.q1,
        question2: validatedData.q2 !== undefined ? String(validatedData.q2) : null,
        question3: validatedData.q3,
        question4: validatedData.q4,
        question5: validatedData.q5,
        paymentStatus: validatedData.paymentStatus || null,
        // New fields
        keyContributions: validatedData.keyContributions || null,
        extraInitiatives: validatedData.extraInitiatives || null,
        performanceLacking: validatedData.performanceLacking || null,
        disciplineRating: validatedData.disciplineRating || null,
        disciplineComment: validatedData.disciplineComment || null,
        daysOffTaken: validatedData.daysOffTaken || null,
        impactRating: validatedData.impactRating || null,
        impactComment: validatedData.impactComment || null,
        qualityRating: validatedData.qualityRating || null,
        qualityComment: validatedData.qualityComment || null,
        collaborationRating: validatedData.collaborationRating || null,
        collaborationComment: validatedData.collaborationComment || null,
        skillGrowthRating: validatedData.skillGrowthRating || null,
        skillGrowthComment: validatedData.skillGrowthComment || null,
        readinessPromotion: validatedData.readinessPromotion || null,
        readinessComment: validatedData.readinessComment || null,
        compensationRecommendation: validatedData.compensationRecommendation || null,
      })

      // Get the appraisal record to get its ID
      const appraisalRecord = await db.getAppraisalByEmployeeId(
        validatedData.employeeId,
        currentPeriod.id
      )

      if (!appraisalRecord) {
        throw new Error("Failed to create appraisal record")
      }

      // Now submit it
      await db.submitAppraisal(appraisalRecord.id, validatedData.paymentStatus)

      // Create approval workflow for multi-level approvals
      try {
        const { createApprovalWorkflow } = await import('../data/approvals')
        const workflowResult = await createApprovalWorkflow(appraisalRecord.id, validatedData.employeeId)

        if (workflowResult.success) {
          console.log("✅ [APPROVALS] Approval workflow created:", workflowResult.workflowId)
        } else {
          console.error("❌ [APPROVALS] Failed to create workflow:", workflowResult.error)
          // Log detailed error for debugging but don't fail the submission
          console.error("🔍 [APPROVALS] Workflow creation details:", {
            appraisalId: appraisalRecord.id,
            employeeId: validatedData.employeeId,
            error: workflowResult.error
          })
        }
      } catch (workflowError) {
        console.error("🚨 [APPROVALS] Error creating approval workflow:", workflowError)
        console.error("🔍 [APPROVALS] Workflow creation exception details:", {
          appraisalId: appraisalRecord.id,
          employeeId: validatedData.employeeId,
          error: workflowError instanceof Error ? workflowError.message : String(workflowError),
          stack: workflowError instanceof Error ? workflowError.stack : undefined
        })
        // Don't fail the submission if workflow creation fails, but ensure we have visibility
      }

      console.log("Appraisal submitted successfully to database.")
    } catch (dbError) {
      console.error("Database error during appraisal submission:", dbError)
      throw new Error("Failed to save appraisal to database")
    }

    // Invalidate performance stats cache
    invalidatePerformanceStatsCache(session.userId)

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath(`/dashboard/appraisal/${validatedData.employeeId}`)
    revalidatePath("/dashboard/approvals")

    // Ensure the response is serializable
    const response = { success: true as const, message: "Appraisal submitted successfully." }
    try {
      JSON.stringify(response)
    } catch (e) {
      console.error('❌ [DEBUG] Response not serializable:', e)
      return { success: true as const, message: "Appraisal submitted successfully." }
    }
    return response
  } catch (error) {
    console.error('❌ [DEBUG] Server action error:', error)
    const errorResponse = handleServerActionError(error)
    // Ensure error response is serializable
    try {
      JSON.stringify(errorResponse)
    } catch (e) {
      console.error('❌ [DEBUG] Error response not serializable:', e)
      return { success: false as const, error: "An unexpected error occurred." }
    }
    return errorResponse
  }
}

export async function createAppraisalRevisionAction(appraisalId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'appraisal-revision', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate appraisal ID
    if (!appraisalId || typeof appraisalId !== 'string') {
      throw new ValidationError("Valid appraisal ID is required")
    }

    // Create the revision in the database
    try {
      const revisedAppraisal = await db.createAppraisalRevision(appraisalId, {})
      
      console.log(`Created revision for appraisal ${appraisalId}, new revision number: ${revisedAppraisal.revision_number}`)

      // Log the action
      await logUserAction('appraisal:revision', {
        appraisalId,
        revisionNumber: revisedAppraisal.revision_number,
        managerId: session.userId
      })

      // Revalidate relevant pages
      revalidatePath("/dashboard")
      revalidatePath(`/dashboard/appraisal/${revisedAppraisal.employee_id}`)
      revalidatePath("/dashboard/approvals")

      return { 
        success: true, 
        message: "Appraisal revision created successfully. You can now edit and resubmit it.",
        revisionNumber: revisedAppraisal.revision_number
      }
    } catch (dbError) {
      console.error("Database error during revision creation:", dbError)
      throw new Error("Failed to create appraisal revision")
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function resubmitAppraisalRevisionAction(appraisal: Partial<AppraisalDetails>) {
  try {
    console.log('🚀 [DEBUG] Starting appraisal revision resubmission')

    // Check if the input data is serializable
    try {
      JSON.stringify(appraisal)
      console.log('✅ [DEBUG] Input revision appraisal data is serializable')
    } catch (serializationError) {
      console.error('❌ [DEBUG] Input revision appraisal data is not serializable:', serializationError)
      throw new ValidationError('Invalid form data: contains non-serializable values')
    }

    // Authentication check
    const session = await validateSession()

    // Rate limiting (stricter for submissions)
    if (!checkRateLimit(session.userId, 'appraisal-resubmit', 5, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate submission data (strict validation)
    const submissionData = {
      ...appraisal,
      managerId: session.userId,
      status: 'submitted' as const,
    }

    let validatedData
    try {
      validatedData = validateAppraisalSubmission(submissionData)
      debug.log('✅ [DEBUG] Resubmit validation successful')
    } catch (validationError) {
      debug.error('❌ [DEBUG] Resubmit validation failed:', validationError)
      throw validationError
    }

    // Additional business logic validation
    if (!validatedData.employeeId || !validatedData.id) {
      throw new ValidationError("Employee ID and Appraisal ID are required")
    }

    // Log the action
    await logUserAction('appraisal:resubmit', {
      appraisalId: validatedData.id,
      employeeId: validatedData.employeeId,
      managerId: session.userId,
      resubmittedAt: new Date().toISOString()
    })

    console.log(`Resubmitting appraisal revision ${validatedData.id} for employee ${validatedData.employeeId}`)

    // Save the updated draft data first, then resubmit
    try {
      console.log(`🔄 [DEBUG] Resubmitting revision with ID: ${validatedData.id}`)

      // For revisions, we should update the specific revision record, not create a new draft
      // First, update the specific revision record with the new data
      await db.updateAppraisalRevision(validatedData.id, {
        question1: validatedData.q1,
        question2: validatedData.q2 !== undefined ? String(validatedData.q2) : null,
        question3: validatedData.q3,
        question4: validatedData.q4,
        question5: validatedData.q5,
        paymentStatus: validatedData.paymentStatus || null,
        // New fields
        keyContributions: validatedData.keyContributions || null,
        extraInitiatives: validatedData.extraInitiatives || null,
        performanceLacking: validatedData.performanceLacking || null,
        disciplineRating: validatedData.disciplineRating || null,
        disciplineComment: validatedData.disciplineComment || null,
        daysOffTaken: validatedData.daysOffTaken || null,
        impactRating: validatedData.impactRating || null,
        impactComment: validatedData.impactComment || null,
        qualityRating: validatedData.qualityRating || null,
        qualityComment: validatedData.qualityComment || null,
        collaborationRating: validatedData.collaborationRating || null,
        collaborationComment: validatedData.collaborationComment || null,
        skillGrowthRating: validatedData.skillGrowthRating || null,
        skillGrowthComment: validatedData.skillGrowthComment || null,
        readinessPromotion: validatedData.readinessPromotion || null,
        readinessComment: validatedData.readinessComment || null,
        compensationRecommendation: validatedData.compensationRecommendation || null,
      })

      debug.log(`📝 [DEBUG] Updated revision data for appraisal ${validatedData.id}`)

      // Resubmit the specific revision
      await db.resubmitAppraisalRevision(validatedData.id)

      debug.log(`✅ [DEBUG] Appraisal revision ${validatedData.id} resubmitted successfully to database.`)
    } catch (dbError) {
      debug.error("❌ [DEBUG] Database error during revision resubmission:", dbError)
      throw new Error("Failed to resubmit appraisal revision")
    }

    // Invalidate performance stats cache
    invalidatePerformanceStatsCache(session.userId)

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath(`/dashboard/appraisal/${validatedData.employeeId}`)
    revalidatePath("/dashboard/approvals")

    // Ensure the response is serializable
    const response = { success: true as const, message: "Appraisal revision resubmitted successfully." }
    try {
      JSON.stringify(response)
    } catch (e) {
      console.error('❌ [DEBUG] Response not serializable:', e)
      return { success: true as const, message: "Appraisal revision resubmitted successfully." }
    }
    return response
  } catch (error) {
    console.error('❌ [DEBUG] Revision resubmission error:', error)
    const errorResponse = handleServerActionError(error)
    // Ensure error response is serializable
    try {
      JSON.stringify(errorResponse)
    } catch (e) {
      console.error('❌ [DEBUG] Error response not serializable:', e)
      return { success: false as const, error: "An unexpected error occurred." }
    }
    return errorResponse
  }
}

export async function createRevisionFromSubmittedAction(appraisalId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'appraisal-revision', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('appraisal:write')

    // Validate appraisal ID
    if (!appraisalId || typeof appraisalId !== 'string') {
      throw new ValidationError("Valid appraisal ID is required")
    }

    // Create the revision in the database
    try {
      const revisedAppraisal = await db.createAppraisalRevision(appraisalId, {})

      console.log(`Created new revision from submitted revision ${appraisalId}, new revision number: ${revisedAppraisal.revision_number}`)

      // Log the action
      await logUserAction('appraisal:revision-from-submitted', {
        originalAppraisalId: appraisalId,
        newRevisionId: revisedAppraisal.id,
        revisionNumber: revisedAppraisal.revision_number,
        managerId: session.userId
      })

      // Revalidate relevant pages
      revalidatePath("/dashboard")
      revalidatePath(`/dashboard/appraisal/${revisedAppraisal.employee_id}`)
      revalidatePath("/dashboard/approvals")

      return {
        success: true,
        message: "New revision created successfully. You can now edit and resubmit it.",
        revisionNumber: revisedAppraisal.revision_number
      }
    } catch (dbError) {
      console.error("Database error during revision creation from submitted:", dbError)
      throw new Error("Failed to create new revision")
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}