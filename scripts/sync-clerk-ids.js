#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to sync Clerk IDs with Supabase appy_managers table
 * This script will:
 * 1. Get all managers from Supabase with placeholder/temp user_ids
 * 2. Look up their real Clerk IDs by email
 * 3. Update the database with correct Clerk IDs
 * 
 * Usage: node scripts/sync-clerk-ids.js [--dry-run] [--force]
 */

const { clerkClient } = require('@clerk/clerk-sdk-node')
const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function getManagersNeedingSync() {
  console.log('🔍 Finding managers that need Clerk ID sync...')
  
  const { data: managers, error } = await supabase
    .from('appy_managers')
    .select('user_id, full_name, email, role')
    .or('user_id.like.*_manager_id,user_id.like.*_clerk_user_id_placeholder,user_id.like.temp_*')
    .eq('active', true)
  
  if (error) {
    throw new Error(`Failed to fetch managers: ${error.message}`)
  }
  
  console.log(`📋 Found ${managers.length} managers needing sync:`)
  managers.forEach(m => {
    console.log(`   ${m.full_name} (${m.email}) - Current ID: ${m.user_id}`)
  })
  
  return managers
}

async function lookupClerkId(email) {
  try {
    const users = await clerkClient.users.getUserList({
      emailAddress: [email],
      limit: 1
    })
    
    if (users.data.length > 0) {
      return users.data[0].id
    }
    return null
  } catch (error) {
    console.error(`Error looking up ${email}:`, error.message)
    return null
  }
}

async function updateManagerClerkId(currentUserId, newClerkId, email) {
  console.log(`🔄 Updating manager ID from ${currentUserId} to ${newClerkId}`)

  // Use a transaction to handle the foreign key constraint properly
  // We need to temporarily create a new manager record, update relationships, then remove the old one

  try {
    // Step 1: Get the current manager data
    const { data: currentManager, error: fetchError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', currentUserId)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch current manager data: ${fetchError.message}`)
    }

    console.log(`📋 Current manager data:`, currentManager.full_name)

    // Step 2: Create a new manager record with the correct Clerk ID
    const { error: insertError } = await supabase
      .from('appy_managers')
      .insert({
        user_id: newClerkId,
        full_name: currentManager.full_name,
        email: currentManager.email,
        department_id: currentManager.department_id,
        active: currentManager.active,
        manager_id: currentManager.manager_id,
        role: currentManager.role
      })

    if (insertError) {
      throw new Error(`Failed to create new manager record: ${insertError.message}`)
    }

    console.log(`✅ Created new manager record with Clerk ID`)

    // Step 3: Update all employee-manager relationships to use the new manager_id
    const { error: emUpdateError } = await supabase
      .from('appy_employee_managers')
      .update({ manager_id: newClerkId })
      .eq('manager_id', currentUserId)

    if (emUpdateError) {
      console.log(`⚠️  Warning: Failed to update employee_managers: ${emUpdateError.message}`)
    } else {
      console.log(`✅ Updated employee-manager relationships`)
    }

    // Step 4: Delete the old manager record
    const { error: deleteError } = await supabase
      .from('appy_managers')
      .delete()
      .eq('user_id', currentUserId)

    if (deleteError) {
      throw new Error(`Failed to delete old manager record: ${deleteError.message}`)
    }

    console.log(`✅ Removed old manager record`)

  } catch (error) {
    // If anything fails, try to clean up the new record if it was created
    console.log(`🧹 Cleaning up after error...`)
    await supabase
      .from('appy_managers')
      .delete()
      .eq('user_id', newClerkId)

    throw error
  }
}

async function syncClerkIds(dryRun = false, force = false) {
  console.log(`🚀 Starting Clerk ID sync ${dryRun ? '(DRY RUN)' : '(LIVE)'}`)
  
  const managers = await getManagersNeedingSync()
  
  if (managers.length === 0) {
    console.log('✅ No managers need syncing!')
    return
  }
  
  if (!force && !dryRun) {
    console.log('\n⚠️  This will update the database. Use --dry-run to test first, or --force to proceed.')
    return
  }
  
  const results = {
    success: [],
    notFound: [],
    errors: []
  }
  
  console.log(`\n🔄 Processing ${managers.length} managers...`)
  
  for (const manager of managers) {
    process.stdout.write(`Looking up ${manager.email}... `)
    
    try {
      const clerkId = await lookupClerkId(manager.email)
      
      if (clerkId) {
        console.log(`✅ Found: ${clerkId}`)
        
        if (!dryRun) {
          await updateManagerClerkId(manager.user_id, clerkId, manager.email)
          console.log(`   ✅ Updated in database`)
        } else {
          console.log(`   📝 Would update: ${manager.user_id} -> ${clerkId}`)
        }
        
        results.success.push({
          name: manager.full_name,
          email: manager.email,
          oldId: manager.user_id,
          newId: clerkId
        })
      } else {
        console.log(`❌ Not found in Clerk`)
        results.notFound.push({
          name: manager.full_name,
          email: manager.email,
          currentId: manager.user_id
        })
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
      results.errors.push({
        name: manager.full_name,
        email: manager.email,
        error: error.message
      })
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  // Summary
  console.log(`\n📊 SYNC SUMMARY:`)
  console.log(`✅ Successfully ${dryRun ? 'found' : 'synced'}: ${results.success.length}`)
  console.log(`❌ Not found in Clerk: ${results.notFound.length}`)
  console.log(`💥 Errors: ${results.errors.length}`)
  
  if (results.notFound.length > 0) {
    console.log(`\n❌ Managers not found in Clerk:`)
    results.notFound.forEach(m => {
      console.log(`   ${m.name} (${m.email})`)
    })
  }
  
  if (results.errors.length > 0) {
    console.log(`\n💥 Errors encountered:`)
    results.errors.forEach(m => {
      console.log(`   ${m.name} (${m.email}): ${m.error}`)
    })
  }
  
  if (results.success.length > 0 && dryRun) {
    console.log(`\n🚀 To apply these changes, run: node scripts/sync-clerk-ids.js --force`)
  }
  
  return results
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2)
  const dryRun = args.includes('--dry-run')
  const force = args.includes('--force')
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: node scripts/sync-clerk-ids.js [options]')
    console.log('')
    console.log('Options:')
    console.log('  --dry-run    Show what would be changed without making changes')
    console.log('  --force      Actually update the database')
    console.log('  --help, -h   Show this help message')
    console.log('')
    console.log('Examples:')
    console.log('  node scripts/sync-clerk-ids.js --dry-run   # Test run')
    console.log('  node scripts/sync-clerk-ids.js --force     # Live update')
    process.exit(0)
  }
  
  syncClerkIds(dryRun, force)
    .then(() => {
      console.log('\n✅ Sync complete!')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Sync failed:', error)
      process.exit(1)
    })
}

module.exports = { syncClerkIds, getManagersNeedingSync }
