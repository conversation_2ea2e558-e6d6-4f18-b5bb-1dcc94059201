#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix placeholder manager IDs in appy_employees table
 * Maps old placeholder IDs to new Clerk IDs based on successful sync results
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Mapping of old placeholder IDs to new Clerk IDs (from successful sync)
const managerIdMapping = {
  'natalie_manager_id': 'user_30HKIngDzJTzSsQP0h7rgLb7BLK',
  'ned_manager_id': 'user_30HISeYH4NtmA0OeOOFLnxXCxvd',
  'joey_manager_id': 'user_30HHjoBgIUfIsYZPLHPWshNe4CR',
  'gian_manager_id': 'user_30HD4M66mGVVsAZ3YIWjOLoIo3R'
}

async function getEmployeesWithPlaceholderIds() {
  console.log('🔍 Finding employees with placeholder manager IDs...')
  
  const placeholderPatterns = Object.keys(managerIdMapping)
  const orConditions = placeholderPatterns.map(id => `manager_id.eq.${id}`).join(',')
  
  const { data: employees, error } = await supabase
    .from('appy_employees')
    .select('id, full_name, manager_id')
    .or(orConditions)
  
  if (error) {
    throw new Error(`Failed to fetch employees: ${error.message}`)
  }
  
  console.log(`📋 Found ${employees.length} employees with mappable placeholder IDs:`)
  employees.forEach(emp => {
    const newId = managerIdMapping[emp.manager_id]
    console.log(`   ${emp.full_name}: ${emp.manager_id} -> ${newId}`)
  })
  
  return employees
}

async function updateEmployeeManagerId(employeeId, oldManagerId, newManagerId, employeeName) {
  console.log(`🔄 Updating ${employeeName}: ${oldManagerId} -> ${newManagerId}`)
  
  const { error } = await supabase
    .from('appy_employees')
    .update({ manager_id: newManagerId })
    .eq('id', employeeId)
    .eq('manager_id', oldManagerId) // Extra safety check
  
  if (error) {
    throw new Error(`Failed to update ${employeeName}: ${error.message}`)
  }
  
  console.log(`✅ Updated ${employeeName}`)
}

async function fixEmployeeManagerIds(dryRun = false) {
  console.log(`🚀 Starting employee manager ID fix ${dryRun ? '(DRY RUN)' : '(LIVE)'}`)
  
  const employees = await getEmployeesWithPlaceholderIds()
  
  if (employees.length === 0) {
    console.log('✅ No employees need manager ID updates!')
    return
  }
  
  const results = {
    success: [],
    errors: []
  }
  
  console.log(`\n🔄 Processing ${employees.length} employees...`)
  
  for (const employee of employees) {
    const newManagerId = managerIdMapping[employee.manager_id]
    
    if (!newManagerId) {
      console.log(`⚠️  Skipping ${employee.full_name} - no mapping for ${employee.manager_id}`)
      continue
    }
    
    try {
      if (!dryRun) {
        await updateEmployeeManagerId(employee.id, employee.manager_id, newManagerId, employee.full_name)
      } else {
        console.log(`📝 Would update ${employee.full_name}: ${employee.manager_id} -> ${newManagerId}`)
      }
      
      results.success.push({
        name: employee.full_name,
        oldId: employee.manager_id,
        newId: newManagerId
      })
    } catch (error) {
      console.log(`❌ Error updating ${employee.full_name}: ${error.message}`)
      results.errors.push({
        name: employee.full_name,
        error: error.message
      })
    }
    
    // Small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  // Summary
  console.log(`\n📊 FIX SUMMARY:`)
  console.log(`✅ Successfully ${dryRun ? 'would update' : 'updated'}: ${results.success.length}`)
  console.log(`💥 Errors: ${results.errors.length}`)
  
  if (results.errors.length > 0) {
    console.log(`\n💥 Errors encountered:`)
    results.errors.forEach(e => {
      console.log(`   ${e.name}: ${e.error}`)
    })
  }
  
  if (!dryRun && results.success.length > 0) {
    console.log(`\n✅ Successfully updated ${results.success.length} employee manager IDs!`)
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2)
  const dryRun = args.includes('--dry-run')
  const force = args.includes('--force')
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: node scripts/fix-employee-manager-ids.js [options]')
    console.log('')
    console.log('Options:')
    console.log('  --dry-run    Show what would be changed without making changes')
    console.log('  --force      Actually update the database')
    console.log('  --help, -h   Show this help message')
    console.log('')
    console.log('Examples:')
    console.log('  node scripts/fix-employee-manager-ids.js --dry-run   # Test run')
    console.log('  node scripts/fix-employee-manager-ids.js --force     # Live update')
    process.exit(0)
  }
  
  if (!dryRun && !force) {
    console.log('\n⚠️  This will update the database. Use --dry-run to test first, or --force to proceed.')
    process.exit(1)
  }
  
  fixEmployeeManagerIds(dryRun)
    .then(() => {
      console.log('\n✅ Fix complete!')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Fix failed:', error)
      process.exit(1)
    })
}

module.exports = { fixEmployeeManagerIds }
