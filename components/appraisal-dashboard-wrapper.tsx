"use client"

import { AppraisalDashboardTable } from "@/components/appraisal-dashboard-table"
import { bulkAppraisalAction } from "@/lib/actions"
import { toast } from "sonner"
import type { EmployeeAppraisal, Manager } from "@/lib/types"

interface AppraisalDashboardWrapperProps {
  data: EmployeeAppraisal[]
  managers: Manager[]
}

export function AppraisalDashboardWrapper({ data, managers }: AppraisalDashboardWrapperProps) {

  const handleBulkAction = async (action: string, selectedIds: string[]) => {
    try {
      const result = await bulkAppraisalAction(action, selectedIds)
      if (result.success) {
        toast.success(result.message || 'Action completed successfully')
        // Revalidate the page to show updated data
        window.location.reload()
      } else {
        toast.error(result.error || 'Action failed')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Bulk action error:', error)
    }
  }

  return <AppraisalDashboardTable data={data} managers={managers} onBulkAction={handleBulkAction} />
}