"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  DollarSign, 
  Users, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  TrendingUp,
  Calendar,
  FileText
} from "lucide-react"
import type { AccountingStats } from "@/lib/types"

interface AccountingStatsCardsProps {
  stats: AccountingStats
}

// Helper function to format currency
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Helper function to format large numbers
function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

export function AccountingStatsCards({ stats }: AccountingStatsCardsProps) {
  console.log('📊 [DEBUG] AccountingStatsCards - Rendering stats:', stats)

  const cards = [
    {
      title: "Ready to Pay",
      value: stats.readyToPay.toString(),
      description: "Employees approved for payment",
      icon: CheckCircle,
      color: "text-emerald-600",
      bgColor: "bg-emerald-100",
      trend: `${Math.round((stats.readyToPay / stats.totalEmployees) * 100)}% of total`
    },
    {
      title: "Total Employees",
      value: stats.totalEmployees.toString(),
      description: "Active employees this period",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      trend: `${stats.hourlyEmployees} hourly, ${stats.monthlyEmployees} monthly`
    },
    {
      title: "Submitted Appraisals",
      value: stats.submittedAppraisals.toString(),
      description: "Completed appraisals",
      icon: FileText,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      trend: `${Math.round((stats.submittedAppraisals / stats.totalEmployees) * 100)}% completion`
    },
    {
      title: "Pending Appraisals",
      value: stats.pendingAppraisals.toString(),
      description: "Awaiting completion",
      icon: Clock,
      color: "text-amber-600",
      bgColor: "bg-amber-100",
      trend: stats.pendingAppraisals > 0 ? "In progress" : "All complete"
    }
  ]

  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {cards.map((card, index) => {
        const Icon = card.icon
        return (
          <Card key={card.title} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="mobile-caption font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              <div className={`${card.bgColor} p-2 rounded-full flex-shrink-0`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-xl sm:text-2xl font-bold tracking-tight">
                  {card.value}
                </div>
                <p className="mobile-caption text-muted-foreground">
                  {card.description}
                </p>
                <div className="flex items-center space-x-1">
                  <Badge
                    variant="secondary"
                    className="text-xs font-normal bg-gray-100 text-gray-700"
                  >
                    {card.trend}
                  </Badge>
                </div>
              </div>
            </CardContent>
            
            {/* Subtle background pattern for visual interest */}
            <div className="absolute top-0 right-0 w-16 h-16 opacity-5">
              <Icon className="w-full h-full" />
            </div>
          </Card>
        )
      })}
    </div>
  )
}

// Loading skeleton component
export function AccountingStatsCardsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index} className="animate-pulse">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 w-24 bg-gray-200 rounded"></div>
            <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="h-8 w-16 bg-gray-200 rounded"></div>
              <div className="h-3 w-32 bg-gray-200 rounded"></div>
              <div className="h-5 w-20 bg-gray-200 rounded"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Summary component for quick overview
export function AccountingSummary({ stats }: AccountingStatsCardsProps) {
  const completionRate = Math.round((stats.submittedAppraisals / stats.totalEmployees) * 100)
  const paymentRate = Math.round((stats.readyToPay / stats.totalEmployees) * 100)
  
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-600" />
          Accounting Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-green-600">
              {stats.hourlyEmployees}
            </div>
            <div className="text-sm text-muted-foreground">Hourly Employees</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {completionRate}%
            </div>
            <div className="text-sm text-muted-foreground">Completion Rate</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-emerald-600">
              {paymentRate}%
            </div>
            <div className="text-sm text-muted-foreground">Ready for Payment</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
