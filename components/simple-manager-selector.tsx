'use client'

import React from 'react'
import { Crown, User } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import type { Manager } from '@/lib/types'

interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

interface SimpleManagerSelectorProps {
  managers: Manager[]
  selectedManagers: SelectedManager[]
  onManagersChange: (managers: SelectedManager[]) => void
  disabled?: boolean
  className?: string
}

export function SimpleManagerSelector({
  managers,
  selectedManagers,
  onManagersChange,
  disabled = false,
  className
}: SimpleManagerSelectorProps) {
  const handleManagerToggle = (managerId: string, managerName: string, checked: boolean) => {
    if (checked) {
      // Add manager
      const newManager = {
        id: managerId,
        name: managerName,
        isPrimary: selectedManagers.length === 0 // First manager becomes primary
      }
      onManagersChange([...selectedManagers, newManager])
    } else {
      // Remove manager
      const updated = selectedManagers.filter(m => m.id !== managerId)
      
      // If we removed the primary manager and there are others, make the first one primary
      const removedManager = selectedManagers.find(m => m.id === managerId)
      if (removedManager?.isPrimary && updated.length > 0) {
        updated[0].isPrimary = true
      }
      
      onManagersChange(updated)
    }
  }

  const handlePrimaryChange = (primaryManagerId: string) => {
    const updated = selectedManagers.map(manager => ({
      ...manager,
      isPrimary: manager.id === primaryManagerId
    }))
    onManagersChange(updated)
  }

  const primaryManagerId = selectedManagers.find(m => m.isPrimary)?.id

  if (managers.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Manager Assignment</CardTitle>
          <CardDescription>
            No managers available. Please add managers first.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <User className="h-5 w-5" />
          Manager Assignment
        </CardTitle>
        <CardDescription>
          Select one or more managers for this employee. The primary manager will be the main point of contact.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Manager Selection */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Select Managers</h4>
          <div className="grid gap-3">
            {managers.map((manager) => {
              const isSelected = selectedManagers.some(m => m.id === manager.id)
              return (
                <div key={manager.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50">
                  <Checkbox
                    id={`manager-${manager.id}`}
                    checked={isSelected}
                    onCheckedChange={(checked) => 
                      handleManagerToggle(manager.id, manager.fullName, checked as boolean)
                    }
                    disabled={disabled}
                  />
                  <Label 
                    htmlFor={`manager-${manager.id}`}
                    className="flex-1 cursor-pointer"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{manager.fullName}</div>
                        <div className="text-sm text-muted-foreground">{manager.email}</div>
                      </div>
                      {isSelected && selectedManagers.find(m => m.id === manager.id)?.isPrimary && (
                        <Badge variant="secondary" className="ml-2">
                          <Crown className="h-3 w-3 mr-1" />
                          Primary
                        </Badge>
                      )}
                    </div>
                  </Label>
                </div>
              )
            })}
          </div>
        </div>

        {/* Primary Manager Selection */}
        {selectedManagers.length > 1 && (
          <div className="space-y-4 pt-4 border-t">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Crown className="h-4 w-4" />
              Primary Manager
            </h4>
            <p className="text-sm text-muted-foreground">
              Choose which manager should be the primary contact for this employee.
            </p>
            <RadioGroup
              value={primaryManagerId}
              onValueChange={handlePrimaryChange}
              disabled={disabled}
            >
              {selectedManagers.map((manager) => (
                <div key={manager.id} className="flex items-center space-x-2">
                  <RadioGroupItem value={manager.id} id={`primary-${manager.id}`} />
                  <Label htmlFor={`primary-${manager.id}`} className="cursor-pointer">
                    {manager.name}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* Summary */}
        {selectedManagers.length > 0 && (
          <div className="pt-4 border-t">
            <h4 className="font-medium text-sm mb-2">Summary</h4>
            <div className="text-sm text-muted-foreground">
              {selectedManagers.length === 1 ? (
                <>This employee will report to <strong>{selectedManagers[0].name}</strong></>
              ) : (
                <>
                  This employee will report to <strong>{selectedManagers.length} managers</strong>, 
                  with <strong>{selectedManagers.find(m => m.isPrimary)?.name}</strong> as the primary manager
                </>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
