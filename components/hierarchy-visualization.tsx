"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, User, Crown, Shield, UserCheck } from "lucide-react"
import type { HierarchyNode } from "@/lib/types"

interface HierarchyVisualizationProps {
  data: HierarchyNode[]
}

function getRoleIcon(role: string) {
  switch (role) {
    case 'super-admin':
      return <Crown className="h-4 w-4" />
    case 'hr-admin':
      return <Shield className="h-4 w-4" />
    case 'senior-manager':
      return <UserCheck className="h-4 w-4" />
    case 'manager':
      return <Users className="h-4 w-4" />
    default:
      return <User className="h-4 w-4" />
  }
}

function getRoleColor(role: string) {
  switch (role) {
    case 'super-admin':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'hr-admin':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'senior-manager':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'manager':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'employee':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

function HierarchyNodeComponent({ node, level = 0 }: { node: HierarchyNode; level?: number }) {
  const hasChildren = node.children.length > 0
  const marginLeft = level * 24

  return (
    <div className="relative">
      {/* Connecting line from parent */}
      {level > 0 && (
        <div 
          className="absolute top-6 bg-gray-300"
          style={{
            left: `${marginLeft - 12}px`,
            width: '12px',
            height: '1px'
          }}
        />
      )}
      
      {/* Vertical line for children */}
      {hasChildren && (
        <div 
          className="absolute bg-gray-300"
          style={{
            left: `${marginLeft + 8}px`,
            top: '48px',
            width: '1px',
            height: `${node.children.length * 80}px`
          }}
        />
      )}

      <div className="flex items-start space-x-3 mb-4" style={{ marginLeft: `${marginLeft}px` }}>
        {/* Node indicator */}
        <div className="relative z-10 mt-4">
          <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
          </div>
        </div>

        {/* Node content */}
        <Card className="flex-1 max-w-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-sm">{node.name}</h4>
              <div className="flex items-center space-x-1">
                {getRoleIcon(node.role)}
              </div>
            </div>
            
            <div className="space-y-2">
              <Badge 
                variant="outline" 
                className={`text-xs ${getRoleColor(node.role)}`}
              >
                {node.role.replace('-', ' ').toUpperCase()}
              </Badge>
              
              <p className="text-xs text-muted-foreground">
                {node.department}
              </p>
              
              {hasChildren && (
                <p className="text-xs text-muted-foreground">
                  {node.children.filter(c => c.type === 'manager').length} managers, {' '}
                  {node.children.filter(c => c.type === 'employee').length} employees
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Render children */}
      {hasChildren && (
        <div className="space-y-2">
          {node.children.map((child, index) => (
            <HierarchyNodeComponent
              key={child.id}
              node={child}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function HierarchyVisualization({ data }: HierarchyVisualizationProps) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">No hierarchy data available</p>
      </div>
    )
  }

  // Group nodes by department for better organization
  const departmentGroups = data.reduce((groups, node) => {
    const dept = node.department
    if (!groups[dept]) {
      groups[dept] = []
    }
    groups[dept].push(node)
    return groups
  }, {} as Record<string, HierarchyNode[]>)

  return (
    <div className="space-y-8">
      {/* Legend */}
      <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
        <div className="text-sm font-medium mr-4">Roles:</div>
        {[
          { role: 'super-admin', label: 'Super Admin' },
          { role: 'hr-admin', label: 'HR Admin' },
          { role: 'senior-manager', label: 'Senior Manager' },
          { role: 'manager', label: 'Manager' },
          { role: 'employee', label: 'Employee' }
        ].map(({ role, label }) => (
          <div key={role} className="flex items-center space-x-1">
            {getRoleIcon(role)}
            <Badge variant="outline" className={`text-xs ${getRoleColor(role)}`}>
              {label}
            </Badge>
          </div>
        ))}
      </div>

      {/* Hierarchy Tree */}
      <div className="space-y-12">
        {Object.entries(departmentGroups).map(([department, nodes]) => (
          <div key={department} className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              {department} Department
            </h3>
            <div className="space-y-4">
              {nodes.map(node => (
                <HierarchyNodeComponent key={node.id} node={node} />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}